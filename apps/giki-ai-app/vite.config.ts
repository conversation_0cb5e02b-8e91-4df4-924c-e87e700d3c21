import path from 'path';
import { fileURLToPath } from 'url';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';
import viteCompression from 'vite-plugin-compression';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const workspaceRoot = path.resolve(__dirname, '../..');

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  // Use standard Vite environment variable loading
  console.log(`Building in ${mode} mode`);

  // Dynamic import for ESM-only package
  const tailwindcss = (await import('@tailwindcss/vite')).default;

  // Define a polyfill for process
  const processPolyfill = {
    env: {
      NODE_ENV: mode,
    },
    browser: true,
    version: '',
  };

  return {
    root: __dirname,
    cacheDir: path.resolve(__dirname, '.vite-cache'),

    plugins: [
      react({
        babel: {
          parserOpts: {
            plugins: ['decorators-legacy'],
          },
        },
      }),
      tailwindcss(),
      // Production optimization plugins
      ...(mode === 'production'
        ? [
            viteCompression({
              algorithm: 'gzip',
              ext: '.gz',
            }),
            viteCompression({
              algorithm: 'brotliCompress',
              ext: '.br',
            }),
            VitePWA({
              registerType: 'autoUpdate',
              workbox: {
                globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
                maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5MB limit
                runtimeCaching: [
                  {
                    urlPattern: /^https:\/\/api\.giki\.ai\//,
                    handler: 'NetworkFirst',
                    options: {
                      cacheName: 'giki-api-cache',
                      expiration: {
                        maxEntries: 100,
                        maxAgeSeconds: 60 * 60 * 24, // 24 hours
                      },
                    },
                  },
                ],
              },
              manifest: {
                name: 'Giki.AI - Financial Intelligence Platform',
                short_name: 'Giki.AI',
                description:
                  'AI-powered financial transaction categorization and MIS automation',
                theme_color: '#295343',
                background_color: '#ffffff',
                display: 'standalone',
                icons: [
                  {
                    src: '/favicon.png',
                    sizes: '192x192',
                    type: 'image/png',
                  },
                ],
              },
            }),
          ]
        : []),
    ],

    esbuild: {
      logOverride: { 'this-is-undefined-in-esm': 'silent' },
    },

    worker: {
      rollupOptions: {
        maxParallelFileOps: 1,
      },
    },

    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      'process.env': JSON.stringify({ NODE_ENV: mode }),
      process: JSON.stringify(processPolyfill),
      global: 'globalThis',
      'window.process': JSON.stringify(processPolyfill),
    },

    server: {
      port: 4200,
      host: '0.0.0.0',
      hmr: process.env.VITE_DISABLE_HMR !== 'true',
      watch: process.env.VITE_DISABLE_HMR === 'true' ? null : {},
      proxy: {
        '/api': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
          configure: (proxy: any, _options: any) => {
            proxy.on('error', (err: any, _req: any, _res: any) => {
              console.log('proxy error', err);
            });
            proxy.on('proxyReq', (proxyReq: any, req: any, _res: any) => {
              console.log('Sending Request:', req.method, req.url);
            });
          },
        },
      },
      fs: {
        allow: [__dirname, workspaceRoot],
      },
    },

    preview: {
      port: 4200,
      host: '0.0.0.0',
      proxy: {
        '/api': {
          target: 'http://localhost:8000',
          changeOrigin: true,
          secure: false,
          configure: (proxy: any, _options: any) => {
            proxy.on('error', (err: any, _req: any, _res: any) => {
              console.log('proxy error', err);
            });
            proxy.on('proxyReq', (proxyReq: any, req: any, _res: any) => {
              console.log('Sending Request:', req.method, req.url);
            });
          },
        },
      },
    },

    build: {
      outDir: './dist',
      emptyOutDir: true,
      reportCompressedSize: true,
      sourcemap: mode !== 'production',
      minify: mode === 'production' ? 'esbuild' : false,
      target: 'es2020',
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        maxParallelFileOps: 1,
        output: {
          // Optimize chunk splitting for better caching
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            ui: [
              '@radix-ui/react-accordion',
              '@radix-ui/react-dialog',
              '@radix-ui/react-dropdown-menu',
            ],
            charts: ['recharts'],
            utils: ['axios', 'date-fns', 'zod'],
          },
          // Use content hashing for better caching
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
        },
        onwarn: (warning, warn) => {
          // Skip TypeScript warnings in production build
          if (
            warning.code === 'PLUGIN_WARNING' &&
            warning.message.includes('TypeScript')
          ) {
            return;
          }
          warn(warning);
        },
      },
      commonjsOptions: {
        transformMixedEsModules: true,
      },
    },

    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        utils: path.resolve(__dirname, 'src/lib/utils.ts'),
      },
    },

    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./src/test/setup.ts'],
      environmentOptions: {
        jsdom: {
          resources: 'usable',
        },
      },
    },

    optimizeDeps: {
      exclude: ['lucide-react'],
      include: ['react', 'react-dom', 'react-router-dom'],
    },
  };
});
