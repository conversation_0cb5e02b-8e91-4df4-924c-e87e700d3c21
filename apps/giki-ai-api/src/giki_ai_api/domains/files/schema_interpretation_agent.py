"""
Schema Interpretation Agent - Excel/CSV Processing
==================================================

This agent provides intelligent interpretation of diverse Excel/CSV schemas
with debit/credit inference using the ADK v1.3.0 pattern.

Key capabilities:
- Automatic interpretation of any Excel/CSV format
- Smart debit/credit detection using accounting principles
- Regional banking terminology support
- Customer corrections when needed
"""

import json
import logging
import time
from dataclasses import dataclass
from typing import Any

logger = logging.getLogger(__name__)


# Enhanced ADK imports with enterprise file processing capabilities
from google.adk.tools import (
    FunctionTool,
    LongRunningFunctionTool,
    ToolContext,
    load_artifacts,
    preload_memory,
    transfer_to_agent,
)

# Import StandardGikiAgent base class
from ...shared.ai.standard_giki_agent import StandardGikiAgent
from ...shared.ai.vertex_ai_lock import safe_vertex_ai_call


async def safe_schema_ai_call(prompt: str, generation_config: dict = None) -> str:
    """
    Make a safe Vertex AI call for schema interpretation with global concurrency protection.
    
    This ensures schema interpretation doesn't interfere with other Vertex AI operations.
    """
    return await safe_vertex_ai_call(prompt, generation_config)


# Enhanced LLM-powered schema mapping tool with bank format detection
async def suggest_schema_mapping_tool_function(
    file_name: str,
    file_headers: list[str],
    sample_data: list[list[str]],
    column_stats: dict[str, dict[str, Any]] | None = None,
    previous_patterns: dict[str, Any] | None = None,
    **_kwargs,
) -> dict[str, Any]:
    """
    LLM-powered intelligent schema mapping to transaction schema fields.

    Uses Gemini 2.0 Flash for intelligent column interpretation instead of primitive logic.
    Provides accurate mapping to required transaction schema fields with confidence scoring.

    REQUIRED TRANSACTION FIELDS:
    - date: Transaction date (maps to Transaction.date)
    - description: Transaction description (maps to Transaction.description)
    - amount: Transaction amount (maps to Transaction.amount)

    OPTIONAL TRANSACTION FIELDS:
    - account: Account identifier
    - transaction_type: Type of transaction
    - balance: Account balance
    - reference_number: Transaction reference
    - currency: Transaction currency

    Args:
        file_name: Name of the file being analyzed
        file_headers: List of column headers
        sample_data: Sample rows of data

    Returns:
        Dictionary with LLM-powered schema mapping suggestions
    """
    logger.info(f"LLM-powered schema mapping for file: {file_name}")

    try:
        # PHASE 1 ENHANCEMENT: Automatic bank format detection
        from .bank_formats import bank_format_detector
        
        # Detect bank format using AI-enhanced pattern matching
        detected_bank_format, bank_confidence, bank_reasoning = bank_format_detector.detect_bank_format(
            headers=file_headers,
            sample_data=sample_data,
            file_name=file_name
        )
        
        # Log bank detection results
        if detected_bank_format:
            logger.info(f"Bank format detected: {detected_bank_format.bank_name} ({bank_confidence:.2f} confidence)")
        else:
            logger.info("No specific bank format detected, using generic analysis")
        
        # Prepare bank detection context for AI prompt
        bank_detection_context = ""
        if detected_bank_format and bank_confidence > 0.7:
            bank_detection_context = f"""

BANK FORMAT DETECTION RESULTS:
- Detected Bank: {detected_bank_format.bank_name} ({detected_bank_format.bank_code})
- Region: {detected_bank_format.region.value.title()}
- Confidence: {bank_confidence:.2f}
- Reasoning: {bank_reasoning}
- Expected Format: {detected_bank_format.date_format.value}, {detected_bank_format.amount_format.value}
- Currency: {detected_bank_format.currency_symbol}
- Transaction Patterns: {', '.join(detected_bank_format.transaction_patterns[:5])}
- Regional Terms: Debit: {', '.join(detected_bank_format.debit_terms[:3])}, Credit: {', '.join(detected_bank_format.credit_terms[:3])}

BANK-SPECIFIC VALIDATION:
This file appears to match {detected_bank_format.bank_name}'s format. Use this bank's specific column structure and terminology for higher confidence mapping."""
        # Initialize Vertex AI if needed
        import vertexai

        # Get project settings from config
        from ...core.config import settings

        # Initialize Vertex AI with proper project/location settings and service account
        try:
            project_id = settings.VERTEX_PROJECT_ID or "giki-ai-development"
            location = settings.VERTEX_LOCATION or "us-central1"

            # Import required modules for service account authentication
            import os

            from google.oauth2 import service_account

            # Get service account credentials from environment
            credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
            if credentials_path and os.path.exists(credentials_path):
                credentials = service_account.Credentials.from_service_account_file(
                    credentials_path
                )
                vertexai.init(
                    project=project_id, location=location, credentials=credentials
                )
                logger.info(
                    f"Vertex AI initialized with service account credentials, project: {project_id}, location: {location}"
                )
            else:
                # Fallback to default initialization (will use ADC)
                vertexai.init(project=project_id, location=location)
                logger.warning(
                    f"Vertex AI initialized without explicit credentials (using ADC), project: {project_id}, location: {location}"
                )
        except Exception as init_error:
            logger.warning(
                f"Vertex AI already initialized or init failed: {init_error}"
            )

        # Prepare sample data for analysis (limit to first 5 rows for prompt efficiency)
        sample_rows = sample_data[:5] if sample_data else []

        # Clean sample data to ensure JSON serialization works
        # Convert any pandas timestamps or other non-serializable objects to strings
        cleaned_sample_rows = []
        for row in sample_rows:
            cleaned_row = []
            for value in row:
                if value is None:
                    cleaned_row.append(None)
                elif hasattr(value, "strftime"):  # datetime/timestamp objects
                    cleaned_row.append(str(value))
                elif hasattr(value, "__dict__"):  # complex objects
                    cleaned_row.append(str(value))
                else:
                    cleaned_row.append(value)
            cleaned_sample_rows.append(cleaned_row)

        # Get prompt from centralized registry
        from ...shared.ai.prompt_registry import get_prompt_registry

        prompt_registry = get_prompt_registry()
        prompt = prompt_registry.get("schema_interpretation_main")

        # Track performance
        start_time = time.time()

        # Prepare enhanced context for AI prompt
        statistical_context = ""
        if column_stats:
            statistical_context = f"\n\nSTATISTICAL ANALYSIS:\n{json.dumps(column_stats, indent=2, default=str)}"

        learning_context = ""
        if previous_patterns:
            learning_context = f"\n\nPREVIOUS PATTERNS:\n{json.dumps(previous_patterns, indent=2, default=str)}"

        # Format prompt with enhanced variables including bank detection
        mapping_prompt = prompt.format(
            file_name=file_name,
            file_headers=file_headers,
            sample_data=json.dumps(cleaned_sample_rows, indent=2, default=str),
            statistical_context=statistical_context,
            learning_context=learning_context,
        )
        
        # Add bank detection context to the prompt
        if bank_detection_context:
            mapping_prompt += bank_detection_context

        # Generate LLM response using global safe function with concurrency control
        response_text = await safe_schema_ai_call(
            mapping_prompt,
            generation_config=prompt.model_config,
        )

        # Parse JSON response - extract JSON from response
        response_text = response_text.strip()
        logger.info(
            f"AI response: {response_text[:500]}..."
        )  # Log first 500 chars for debugging

        # Try to extract JSON from response (sometimes AI adds extra text)
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            json_text = response_text[json_start:json_end]
            result = json.loads(json_text)
        else:
            raise Exception(f"No valid JSON found in AI response: {response_text}")

        # PHASE 1 ENHANCEMENT: Add bank detection results to schema interpretation
        if detected_bank_format:
            result["bank_detection"] = {
                "detected_bank": {
                    "bank_name": detected_bank_format.bank_name,
                    "bank_code": detected_bank_format.bank_code,
                    "region": detected_bank_format.region.value,
                    "country": detected_bank_format.country,
                    "confidence": bank_confidence,
                    "detection_reasoning": bank_reasoning
                },
                "bank_format_spec": {
                    "date_format": detected_bank_format.date_format.value,
                    "amount_format": detected_bank_format.amount_format.value,
                    "currency_symbol": detected_bank_format.currency_symbol,
                    "decimal_separator": detected_bank_format.decimal_separator,
                    "thousand_separator": detected_bank_format.thousand_separator,
                    "transaction_patterns": detected_bank_format.transaction_patterns,
                    "debit_terms": detected_bank_format.debit_terms,
                    "credit_terms": detected_bank_format.credit_terms
                },
                "validation_enhanced": bank_confidence > 0.8
            }
            
            # Boost overall confidence if bank format is well-detected
            if bank_confidence > 0.8:
                current_confidence = result.get("overall_confidence", 0.5)
                result["overall_confidence"] = min(current_confidence * 1.1, 1.0)
                
                # Add bank-specific validation to summary
                if "summary" in result:
                    result["summary"] += f" Bank format detected: {detected_bank_format.bank_name} (enhances accuracy)."

        # Track performance metrics
        end_time = time.time()
        latency_ms = (end_time - start_time) * 1000
        success = True

        try:
            prompt_registry.track_performance(
                prompt_id=prompt.id,
                version=prompt.version,
                success=success,
                latency_ms=latency_ms,
                metadata={
                    "file_name": file_name,
                    "column_count": len(file_headers),
                    "sample_rows": len(cleaned_sample_rows),
                },
            )
        except Exception as e:
            logger.warning(f"Failed to track performance: {e}")

        logger.info(
            f"LLM schema mapping completed with {len(result.get('column_mappings', []))} mappings (took {latency_ms:.1f}ms)"
        )
        return result

    except Exception as e:
        # Track failure
        try:
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            prompt_registry.track_performance(
                prompt_id=prompt.id,
                version=prompt.version,
                success=False,
                latency_ms=latency_ms,
                metadata={"error": str(e)},
            )
        except Exception:
            pass

        logger.error(f"LLM schema mapping failed: {e}")
        raise Exception(
            f"AI column interpretation failed: {e}. Fix the AI before proceeding."
        )


# Create the ADK tool
# Enhanced ADK tool with async support for schema mapping
suggest_schema_mapping_tool = LongRunningFunctionTool(
    func=suggest_schema_mapping_tool_function
)


async def _detect_hierarchy_with_ai(categories: list[str]) -> dict[str, Any]:
    """
    LLM-powered hierarchy detection using Vertex AI.
    NO PRIMITIVE CODE - Pure AI-powered pattern analysis using Gemini 2.0 Flash.
    """
    try:
        # Create intelligent hierarchy detection prompt
        hierarchy_prompt = f"""
        You are an expert at analyzing financial categorization patterns and detecting hierarchical structures.

        Category Samples: {categories[:10]}

        Analyze these categories and determine:
        1. Are they hierarchical (contain parent > child relationships)?
        2. What separator is used (if any)?
        3. What type of pattern do they follow?

        Return JSON format:
        {{
            "is_hierarchical": true/false,
            "separator": ">|:|/|-|null",
            "pattern_type": "hierarchical|flat",
            "confidence": 0.95,
            "reasoning": "Brief explanation of detected pattern"
        }}

        JSON:"""

        # Generate LLM response with global safe function
        response_text = await safe_schema_ai_call(
            hierarchy_prompt,
            generation_config={
                "temperature": 0.1,  # Low temperature for consistent analysis
                "max_output_tokens": 200,
                "top_p": 0.8,
            },
        )

        # Parse JSON response
        import json

        result = json.loads(response_text.strip())

        return result

    except Exception as e:
        logger.error(f"LLM hierarchy detection failed: {e}")
        raise Exception(
            f"AI hierarchy detection failed: {e}. Fix the AI before proceeding."
        )


# Category hierarchy detection tool
async def detect_category_hierarchy_tool_function(
    categorization_columns: list[str],
    sample_categories: dict[str, list[str]],
    **_kwargs,
) -> dict[str, Any]:
    """
    Tool function for detecting customer categorization hierarchies using LLM analysis.
    NO PRIMITIVE CODE - Uses Vertex AI for intelligent pattern detection.

    Args:
        categorization_columns: List of column names that contain categories
        sample_categories: Sample category values for each column

    Returns:
        Dictionary with detected hierarchy structure
    """
    logger.info(
        f"LLM-powered category hierarchy detection for columns: {categorization_columns}"
    )

    patterns = []

    # Null check to prevent NoneType error
    if sample_categories is None:
        logger.warning("sample_categories is None, returning empty hierarchy detection")
        return {
            "hierarchy_detected": False,
            "categorization_patterns": [],
            "recommended_mapping": {}
        }

    for col_name, categories in sample_categories.items():
        # Use LLM to detect hierarchical patterns instead of primitive string matching
        hierarchy_analysis = await _detect_hierarchy_with_ai(categories)

        if hierarchy_analysis["is_hierarchical"]:
            patterns.append(
                {
                    "column": col_name,
                    "pattern_type": "hierarchical",
                    "separator": hierarchy_analysis["separator"],
                    "examples": categories[:3],
                    "confidence": hierarchy_analysis["confidence"],
                    "reasoning": hierarchy_analysis["reasoning"],
                }
            )
        else:
            patterns.append(
                {
                    "column": col_name,
                    "pattern_type": "flat",
                    "examples": categories[:5],
                    "confidence": hierarchy_analysis["confidence"],
                    "reasoning": hierarchy_analysis["reasoning"],
                }
            )

    return {
        "hierarchy_detected": len(
            [p for p in patterns if p["pattern_type"] == "hierarchical"]
        )
        > 0,
        "categorization_patterns": patterns,
        "recommended_mapping": {
            col: f"category_l{idx + 1}"
            for idx, col in enumerate(categorization_columns)
        },
    }


# Enhanced ADK tool with async support for category hierarchy detection
detect_category_hierarchy_tool = LongRunningFunctionTool(
    func=detect_category_hierarchy_tool_function
)


# Debit/Credit inference tool
async def infer_debit_credit_logic_tool_function(
    transaction_samples: list[dict[str, Any]],
    column_mappings: dict[str, str],
    **_kwargs,
) -> dict[str, Any]:
    """
    Smart debit/credit detection using accounting principles.

    Analyzes transaction patterns to determine debit/credit direction
    based on accounting conventions and regional banking practices.

    Args:
        transaction_samples: Sample transaction data
        column_mappings: Mapped column names to transaction fields

    Returns:
        Dictionary with debit/credit inference results
    """
    logger.info("Inferring debit/credit logic from transaction samples")

    try:
        # Clean transaction samples for JSON serialization
        cleaned_samples = []
        for sample in transaction_samples[:10]:
            # Null check to prevent NoneType error
            if sample is None:
                logger.warning("Found None sample in transaction_samples, skipping")
                continue
            cleaned_sample = {}
            for key, value in sample.items():
                if value is None:
                    cleaned_sample[key] = None
                elif hasattr(value, "strftime"):  # datetime/timestamp objects
                    cleaned_sample[key] = str(value)
                elif hasattr(value, "__dict__"):  # complex objects
                    cleaned_sample[key] = str(value)
                else:
                    cleaned_sample[key] = value
            cleaned_samples.append(cleaned_sample)

        # Create debit/credit inference prompt
        inference_prompt = f"""
        You are an expert in accounting principles and banking conventions.
        Analyze these transaction samples to determine debit/credit direction.
        
        Column Mappings: {json.dumps(column_mappings, indent=2)}
        Transaction Samples (first 10):
        {json.dumps(cleaned_samples, indent=2, default=str)}
        
        ANALYSIS REQUIREMENTS:
        1. Identify if transactions have separate debit/credit columns
        2. If single amount column, determine sign convention (negative for debits or credits)
        3. Look for transaction type indicators (DR/CR, Debit/Credit, Withdrawal/Deposit)
        4. Consider regional banking conventions
        5. Analyze description patterns for transaction direction clues
        
        ACCOUNTING PRINCIPLES:
        - Debits: Expenses, withdrawals, payments out
        - Credits: Income, deposits, payments in
        - Sign conventions vary by bank and region
        
        Return JSON format:
        {{
            "debit_credit_structure": "separate_columns|single_column_signed|single_column_with_type",
            "sign_convention": "negative_is_debit|negative_is_credit|absolute_with_type",
            "debit_indicators": ["withdrawal", "payment", "debit", "-"],
            "credit_indicators": ["deposit", "credit", "refund", "+"],
            "confidence": 0.95,
            "reasoning": "Explanation of detected pattern",
            "recommendations": {{
                "amount_handling": "use_signed_values|split_by_type|use_absolute_with_indicator",
                "type_detection": "column_based|description_based|sign_based"
            }}
        }}
        
        JSON:"""

        # Generate analysis with global safe function
        response_text = await safe_schema_ai_call(
            inference_prompt,
            generation_config={
                "temperature": 0.1,
                "max_output_tokens": 1000,
                "top_p": 0.8,
            },
        )

        # Parse response
        response_text = response_text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Debit/credit inference complete: {result['debit_credit_structure']} structure detected"
        )
        return result

    except Exception as e:
        logger.error(f"Debit/credit inference failed: {e}")
        raise Exception(f"AI debit/credit inference failed: {e}")


# Enhanced ADK tool with async support for debit/credit logic inference
infer_debit_credit_logic_tool = LongRunningFunctionTool(
    func=infer_debit_credit_logic_tool_function
)


# Unified schema mapping tool
async def map_to_unified_schema_tool_function(
    original_schema: dict[str, str],
    debit_credit_info: dict[str, Any],
    regional_info: dict[str, str] | None = None,
    **_kwargs,
) -> dict[str, Any]:
    """
    Convert diverse formats to standardized transaction schema.

    Maps customer-specific schemas to Giki AI's unified transaction format
    while preserving customer categorization data.

    Args:
        original_schema: Original column mappings
        debit_credit_info: Debit/credit inference results
        regional_info: Optional regional banking information

    Returns:
        Dictionary with unified schema mapping
    """
    logger.info("Mapping to unified transaction schema")

    try:
        # Define unified schema structure
        unified_schema = {
            "required_fields": {
                "date": None,
                "description": None,
                "amount": None,
            },
            "optional_fields": {
                "transaction_type": None,
                "balance": None,
                "account": None,
                "reference_number": None,
                "currency": None,
            },
            "categorization_fields": {},
            "metadata_fields": {},
        }

        # Map original schema to unified format
        # Null check to prevent NoneType error
        if original_schema is None:
            logger.warning("original_schema is None, using empty schema")
            original_schema = {}
            
        for original_col, mapped_field in original_schema.items():
            if mapped_field in unified_schema["required_fields"]:
                unified_schema["required_fields"][mapped_field] = original_col
            elif mapped_field in unified_schema["optional_fields"]:
                unified_schema["optional_fields"][mapped_field] = original_col
            elif mapped_field.startswith("category_"):
                unified_schema["categorization_fields"][mapped_field] = original_col
            else:
                unified_schema["metadata_fields"][mapped_field] = original_col

        # Apply debit/credit logic to amount handling
        amount_processing = {
            "method": debit_credit_info.get("debit_credit_structure"),
            "sign_convention": debit_credit_info.get("sign_convention"),
            "debit_indicators": debit_credit_info.get("debit_indicators", []),
            "credit_indicators": debit_credit_info.get("credit_indicators", []),
        }

        # Create transformation rules
        transformation_rules = {
            "date_format_detection": "auto",
            "amount_processing": amount_processing,
            "description_cleaning": "trim_whitespace|remove_extra_spaces",
            "null_handling": "preserve_as_null",
            "categorization_preservation": True,
        }

        result = {
            "unified_schema": unified_schema,
            "transformation_rules": transformation_rules,
            "validation_requirements": {
                "date": "valid_date_format",
                "amount": "numeric_value",
                "description": "non_empty_string",
            },
            "mapping_complete": all(
                v is not None for v in unified_schema["required_fields"].values()
            ),
            "missing_required_fields": [
                k for k, v in unified_schema["required_fields"].items() if v is None
            ],
        }

        logger.info(
            f"Unified schema mapping complete. Missing fields: {result['missing_required_fields']}"
        )
        return result

    except Exception as e:
        logger.error(f"Unified schema mapping failed: {e}")
        return {
            "error": str(e),
            "unified_schema": None,
            "mapping_complete": False,
        }


# Enhanced ADK tool with async support for unified schema mapping
map_to_unified_schema_tool = LongRunningFunctionTool(
    func=map_to_unified_schema_tool_function
)


# Regional variations handler
async def handle_regional_variations_tool_function(
    file_data: dict[str, Any],
    detected_region: str | None = None,
    **_kwargs,
) -> dict[str, Any]:
    """
    Support regional banking terminology and conventions.

    Handles variations in banking formats, date formats, currency symbols,
    and transaction descriptions across different regions.

    Args:
        file_data: File metadata and sample data
        detected_region: Optional detected region/country

    Returns:
        Dictionary with regional handling instructions
    """
    logger.info(f"Handling regional variations for region: {detected_region}")

    try:
        # Create regional analysis prompt
        regional_prompt = f"""
        You are an expert in global banking formats and regional variations.
        Analyze this financial data to detect and handle regional conventions.
        
        File Data Sample: {json.dumps(file_data, indent=2)}
        Detected Region: {detected_region or "Unknown"}
        
        ANALYSIS REQUIREMENTS:
        1. Detect date format (MM/DD/YYYY, DD/MM/YYYY, YYYY-MM-DD, etc.)
        2. Identify currency symbols and formatting
        3. Recognize regional transaction description patterns
        4. Identify regional banking terminology
        5. Detect decimal/thousand separators (1,234.56 vs 1.234,56)
        
        REGIONAL PATTERNS TO DETECT:
        - US: MM/DD/YYYY dates, $ currency, decimal point
        - Europe: DD/MM/YYYY dates, € currency, comma decimal
        - India: DD-MM-YYYY dates, ₹ currency, lakhs/crores
        - UK: DD/MM/YYYY dates, £ currency, varied formats
        
        Return JSON format:
        {{
            "detected_region": "US|Europe|India|UK|Other",
            "date_format": "MM/DD/YYYY|DD/MM/YYYY|YYYY-MM-DD|DD-MM-YYYY",
            "currency": {{
                "symbol": "$|€|₹|£",
                "position": "prefix|suffix",
                "decimal_separator": ".|,",
                "thousand_separator": ",|.|space"
            }},
            "regional_terms": {{
                "debit_terms": ["withdrawal", "debit", "dr"],
                "credit_terms": ["deposit", "credit", "cr"],
                "fee_terms": ["charge", "fee", "commission"]
            }},
            "parsing_instructions": {{
                "date_parser": "strptime_format",
                "amount_parser": "decimal_conversion_rules",
                "description_cleaner": "regional_specific_rules"
            }},
            "confidence": 0.95
        }}
        
        JSON:"""

        # Generate analysis with global safe function
        response_text = await safe_schema_ai_call(
            regional_prompt,
            generation_config={
                "temperature": 0.1,
                "max_output_tokens": 1000,
                "top_p": 0.8,
            },
        )

        # Parse response
        response_text = response_text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            raise Exception("No valid JSON found in AI response")

        logger.info(
            f"Regional handling complete: {result['detected_region']} conventions detected"
        )
        return result

    except Exception as e:
        logger.error(f"Regional variation handling failed: {e}")
        raise Exception(f"AI regional analysis failed: {e}")


# Enhanced ADK tool with async support for regional variations handling
handle_regional_variations_tool = LongRunningFunctionTool(
    func=handle_regional_variations_tool_function
)


# Schema validation tool
async def validate_schema_mapping_tool_function(
    schema_mapping: dict[str, Any],
    sample_data: list[dict[str, Any]],
    **_kwargs,
) -> dict[str, Any]:
    """
    Ensure mapping accuracy before database storage.

    Validates that the schema mapping will correctly process transactions
    and identifies potential issues before data import.

    Args:
        schema_mapping: Proposed schema mapping
        sample_data: Sample transaction data for validation

    Returns:
        Dictionary with validation results and recommendations
    """
    logger.info("Validating schema mapping accuracy")

    validation_results = {
        "is_valid": True,
        "errors": [],
        "warnings": [],
        "recommendations": [],
    }

    try:
        # Check required fields are mapped
        required_fields = ["date", "description", "amount"]
        for field in required_fields:
            if (
                not schema_mapping.get("unified_schema", {})
                .get("required_fields", {})
                .get(field)
            ):
                validation_results["errors"].append(
                    f"Required field '{field}' is not mapped"
                )
                validation_results["is_valid"] = False

        # Validate sample data can be processed
        if sample_data:
            sample = sample_data[0]

            # Check date field
            date_col = (
                schema_mapping.get("unified_schema", {})
                .get("required_fields", {})
                .get("date")
            )
            if date_col and date_col in sample:
                date_value = sample[date_col]
                if not date_value:
                    validation_results["warnings"].append("Empty date values found")

            # Check amount field
            amount_col = (
                schema_mapping.get("unified_schema", {})
                .get("required_fields", {})
                .get("amount")
            )
            if amount_col and amount_col in sample:
                amount_value = sample[amount_col]
                try:
                    float(
                        str(amount_value)
                        .replace(",", "")
                        .replace("$", "")
                        .replace("€", "")
                    )
                except Exception:
                    validation_results["errors"].append(
                        f"Amount value '{amount_value}' cannot be parsed as number"
                    )
                    validation_results["is_valid"] = False

        # Add recommendations
        if schema_mapping.get("unified_schema", {}).get("categorization_fields"):
            validation_results["recommendations"].append(
                "Customer categorization fields detected - these will be preserved for learning"
            )

        if (
            not schema_mapping.get("transformation_rules", {})
            .get("amount_processing", {})
            .get("method")
        ):
            validation_results["warnings"].append(
                "Debit/credit handling not specified - amounts may need manual review"
            )

        validation_results["summary"] = {
            "total_errors": len(validation_results["errors"]),
            "total_warnings": len(validation_results["warnings"]),
            "mapping_confidence": 0.95 if validation_results["is_valid"] else 0.5,
        }

        logger.info(
            f"Schema validation complete: {'PASSED' if validation_results['is_valid'] else 'FAILED'}"
        )
        return validation_results

    except Exception as e:
        logger.error(f"Schema validation failed: {e}")
        validation_results["errors"].append(f"Validation error: {e!s}")
        validation_results["is_valid"] = False
        return validation_results


# Enhanced ADK tool with async support for schema validation
validate_schema_mapping_tool = LongRunningFunctionTool(
    func=validate_schema_mapping_tool_function
)


# Column correction suggestions tool
async def suggest_column_corrections_tool_function(
    original_mappings: dict[str, str],
    validation_errors: list[str],
    user_feedback: dict[str, Any] | None = None,
    **_kwargs,
) -> dict[str, Any]:
    """
    Enable customer corrections when schema interpretation needs adjustment.

    Provides intelligent suggestions for fixing schema mapping issues
    based on validation errors and user feedback.

    Args:
        original_mappings: Original column mappings
        validation_errors: List of validation errors
        user_feedback: Optional user feedback on mappings

    Returns:
        Dictionary with correction suggestions
    """
    logger.info("Generating column correction suggestions")

    try:
        # Create correction suggestion prompt
        correction_prompt = f"""
        You are an expert at helping users correct schema mapping issues.
        Generate helpful suggestions to fix the mapping problems.
        
        Original Mappings: {json.dumps(original_mappings, indent=2)}
        Validation Errors: {validation_errors}
        User Feedback: {json.dumps(user_feedback, indent=2) if user_feedback else "None"}
        
        SUGGESTION REQUIREMENTS:
        1. Provide clear, actionable correction suggestions
        2. Explain why each correction is needed
        3. Offer alternative mapping options
        4. Prioritize user-friendly explanations
        5. Include examples where helpful
        
        Return JSON format:
        {{
            "correction_suggestions": [
                {{
                    "issue": "Missing date field mapping",
                    "suggestion": "Map the 'Transaction Date' column to the date field",
                    "explanation": "Every transaction needs a date for proper chronological ordering",
                    "alternatives": ["Date", "Trans Date", "Value Date"],
                    "example": "Date column should contain values like '2024-01-15' or '15/01/2024'"
                }}
            ],
            "quick_fixes": [
                {{
                    "field": "date",
                    "recommended_column": "Transaction Date",
                    "confidence": 0.9
                }}
            ],
            "general_advice": "Tips for successful schema mapping"
        }}
        
        JSON:"""

        # Generate suggestions with global safe function
        response_text = await safe_schema_ai_call(
            correction_prompt,
            generation_config={
                "temperature": 0.3,  # Slightly higher for creative suggestions
                "max_output_tokens": 1500,
                "top_p": 0.9,
            },
        )

        # Parse response
        response_text = response_text.strip()
        json_start = response_text.find("{")
        json_end = response_text.rfind("}") + 1

        if json_start >= 0 and json_end > json_start:
            result = json.loads(response_text[json_start:json_end])
        else:
            # Fallback suggestions if AI fails
            result = {
                "correction_suggestions": [
                    {
                        "issue": error,
                        "suggestion": "Please review and correct this mapping",
                        "explanation": "This field is required for transaction processing",
                        "alternatives": [],
                    }
                    for error in validation_errors
                ],
                "quick_fixes": [],
                "general_advice": "Ensure all required fields (date, description, amount) are mapped correctly",
            }

        logger.info(
            f"Generated {len(result['correction_suggestions'])} correction suggestions"
        )
        return result

    except Exception as e:
        logger.error(f"Column correction suggestion failed: {e}")
        # Return helpful fallback suggestions
        return {
            "correction_suggestions": [
                {
                    "issue": "Schema mapping needs review",
                    "suggestion": "Please manually review the column mappings",
                    "explanation": str(e),
                    "alternatives": [],
                }
            ],
            "quick_fixes": [],
            "general_advice": "Contact support if mapping issues persist",
        }


# Enhanced ADK tool with async support for column correction suggestions
suggest_column_corrections_tool = LongRunningFunctionTool(
    func=suggest_column_corrections_tool_function
)


@dataclass
class SchemaInterpretationAgentConfig:
    """Configuration for SchemaInterpretationAgent."""

    model_name: str = "gemini-2.0-flash-001"
    project: str | None = None
    location: str | None = None
    tools: list[FunctionTool] | None = None


class SchemaInterpretationAgent(StandardGikiAgent):
    """
    Intelligent interpretation of diverse Excel/CSV schemas with debit/credit inference.

    This agent handles:
    - Automatic interpretation of any Excel/CSV format
    - Smart debit/credit detection using accounting principles
    - Convert diverse formats to standardized transaction schema
    - Support regional banking terminology
    - Ensure mapping accuracy before database storage
    - Enable customer corrections when needed
    """

    def __init__(self, config: SchemaInterpretationAgentConfig | None = None, **kwargs):
        """Initialize the SchemaInterpretationAgent using StandardGikiAgent inheritance."""
        if config is None:
            config = SchemaInterpretationAgentConfig(
                model_name=kwargs.get("model_name", "gemini-2.0-flash-001"),
                project=kwargs.get("project"),
                location=kwargs.get("location"),
            )

        # Store config and extract values first
        self._config = config
        model_name = config.model_name
        project = config.project
        location = config.location

        # Set up interpretation-specific custom tools as per specification
        custom_tools = [
            suggest_schema_mapping_tool,
            detect_category_hierarchy_tool,
            infer_debit_credit_logic_tool,
            map_to_unified_schema_tool,
            handle_regional_variations_tool,
            validate_schema_mapping_tool,
            suggest_column_corrections_tool,
        ]

        # Add advanced ADK artifact management capabilities
        advanced_file_tools = self._create_advanced_file_processing_tools()
        custom_tools.extend(advanced_file_tools)

        if config.tools:
            custom_tools.extend(config.tools)

        # Remove 'instruction' from kwargs to avoid duplicate parameter error
        # StandardGikiAgent generates its own instruction internally
        # Null check to prevent NoneType error
        if kwargs is None:
            kwargs = {}
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != "instruction"}

        # Debug: Log what kwargs we're receiving
        logger.info(
            f"SchemaInterpretationAgent.__init__ received kwargs: {list(kwargs.keys())}"
        )
        logger.info(f"After filtering: {list(filtered_kwargs.keys())}")

        # Initialize StandardGikiAgent with interpretation-specific configuration
        super().__init__(
            name="schema_interpretation_agent",
            description="Intelligent schema interpretation with debit/credit inference",
            custom_tools=custom_tools,
            enable_interactive_tools=False,  # Interpretation is automated
            model_name=model_name,
            project_id=project,
            location=location or "global",
            **filtered_kwargs,
        )

        # Store attributes after initialization
        self._model_name = model_name
        self._project = project
        self._location = location

        # Initialize interpretation-specific components
        # Using shared model instead to avoid concurrency conflicts
        self._model_name = model_name

        logger.info(f"SchemaInterpretationAgent initialized with model: {model_name}")

    def _create_advanced_file_processing_tools(self) -> list:
        """Create advanced ADK tools for enterprise file processing capabilities."""

        async def load_multiple_file_artifacts_tool_function(
            file_paths: list[str], tenant_id: int, processing_mode: str = "batch"
        ) -> dict[str, Any]:
            """Load multiple files as artifacts for comprehensive schema analysis."""
            try:
                context = ToolContext(
                    tenant_id=tenant_id,
                    metadata={
                        "processing_mode": processing_mode,
                        "file_count": len(file_paths),
                        "agent": "schema_interpretation",
                    },
                )

                artifacts = await load_artifacts(
                    artifact_paths=file_paths, context=context
                )

                logger.info(
                    f"Loaded {len(artifacts)} file artifacts for schema interpretation"
                )
                return {
                    "artifacts_loaded": len(artifacts),
                    "file_paths": file_paths,
                    "processing_mode": processing_mode,
                    "ready_for_interpretation": True,
                }
            except Exception as e:
                logger.error(f"Failed to load file artifacts: {e}")
                return {"artifacts_loaded": 0, "error": str(e)}

        async def save_schema_interpretation_memory_tool_function(
            tenant_id: int,
            file_schema: dict[str, Any],
            interpretation_patterns: dict[str, Any],
        ) -> dict[str, Any]:
            """Save learned schema interpretation patterns for future files."""
            try:
                memory_key = f"schema_patterns_tenant_{tenant_id}"
                context = ToolContext(
                    tenant_id=tenant_id,
                    metadata={
                        "schema_learned": True,
                        "file_type": file_schema.get("file_type", "unknown"),
                        "column_count": len(file_schema.get("columns", [])),
                    },
                )

                memory_data = {
                    "file_schema": file_schema,
                    "interpretation_patterns": interpretation_patterns,
                    "learned_at": str(context.metadata.get("timestamp", "now")),
                }

                await preload_memory(
                    memory_key=memory_key, memory_data=memory_data, context=context
                )

                logger.info(
                    f"Saved schema interpretation patterns for tenant {tenant_id}"
                )
                return {
                    "patterns_saved": True,
                    "schema_type": file_schema.get("file_type", "unknown"),
                    "tenant_id": tenant_id,
                }
            except Exception as e:
                logger.error(f"Failed to save schema patterns: {e}")
                return {"patterns_saved": False, "error": str(e)}

        async def coordinate_with_transaction_agent_tool_function(
            interpreted_schema: dict[str, Any], tenant_id: int
        ) -> dict[str, Any]:
            """Coordinate with transaction agent for schema validation."""
            try:
                context = ToolContext(
                    tenant_id=tenant_id,
                    metadata={
                        "coordination_type": "schema_validation",
                        "schema_complexity": len(
                            interpreted_schema.get("field_mappings", {})
                        ),
                    },
                )

                result = await transfer_to_agent(
                    agent_name="transaction_agent",
                    task_description=f"Validate interpreted schema: {interpreted_schema}",
                    context=context,
                )

                logger.info("Coordinated schema validation with transaction agent")
                return {
                    "coordination_successful": True,
                    "validation_result": result,
                    "schema_approved": True,
                }
            except Exception as e:
                logger.warning(
                    f"Agent coordination failed, proceeding with schema: {e}"
                )
                return {"coordination_successful": False, "error": str(e)}

        # Create LongRunningFunctionTool instances for all advanced file capabilities
        return [
            LongRunningFunctionTool(func=load_multiple_file_artifacts_tool_function),
            LongRunningFunctionTool(
                func=save_schema_interpretation_memory_tool_function
            ),
            LongRunningFunctionTool(
                func=coordinate_with_transaction_agent_tool_function
            ),
        ]

    async def interpret_excel_schema(
        self,
        file_name: str,
        file_headers: list[str],
        sample_data: list[list[str]],
        column_stats: dict[str, dict[str, Any]] | None = None,
        previous_patterns: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        """
        Automatic interpretation of any Excel/CSV format with enhanced statistical analysis.

        Args:
            file_name: Name of the file being analyzed
            file_headers: List of column headers
            sample_data: Sample rows of data
            column_stats: Optional statistical analysis of columns (data types, null counts, etc.)
            previous_patterns: Optional previous interpretation patterns for learning

        Returns:
            Dictionary with interpretation results
        """
        logger.info(f"Interpreting schema for file: {file_name}")

        # Use the enhanced schema mapping tool with statistical context
        result = await suggest_schema_mapping_tool_function(
            file_name=file_name,
            file_headers=file_headers,
            sample_data=sample_data,
            column_stats=column_stats,
            previous_patterns=previous_patterns,
        )

        logger.info(
            f"Schema interpretation complete for {file_name}: {result['overall_confidence']:.1%} confidence"
        )
        return result

    async def interpret_multiple_files_batch(
        self,
        files_data: list[dict[str, Any]],
        tenant_id: int,
        context: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """
        TRUE BATCH SCHEMA INTERPRETATION: Process multiple uploaded files in ONE AI call.
        
        This handles the real customer scenario where multiple files are uploaded together.
        Each file gets schema interpretation but they're processed as a batch for efficiency.
        
        Args:
            files_data: List of file data dicts with:
                - file_name: Name of the file
                - file_headers: List of column headers
                - sample_data: Sample rows of data  
                - file_type: CSV, Excel, etc.
            tenant_id: Tenant ID for context
            context: Optional processing context
            
        Returns:
            Dictionary with batch interpretation results for all files
        """
        if not files_data:
            return {"files_processed": 0, "interpretations": [], "batch_summary": {}}
            
        logger.info(f"TRUE BATCH SCHEMA INTERPRETATION: Processing {len(files_data)} files in ONE AI call")
        
        try:
            # Prepare batch file data for AI processing
            file_analysis_data = []
            for i, file_info in enumerate(files_data):
                # Clean sample data for JSON serialization
                cleaned_sample_data = []
                sample_data = file_info.get("sample_data", [])[:5]  # Limit to 5 rows for efficiency
                
                for row in sample_data:
                    cleaned_row = []
                    for value in row:
                        if value is None:
                            cleaned_row.append(None)
                        elif hasattr(value, "strftime"):  # datetime objects
                            cleaned_row.append(str(value))
                        elif hasattr(value, "__dict__"):  # complex objects
                            cleaned_row.append(str(value))
                        else:
                            cleaned_row.append(value)
                    cleaned_sample_data.append(cleaned_row)
                
                file_analysis_data.append({
                    "index": i,
                    "file_name": file_info.get("file_name", f"file_{i}"),
                    "file_type": file_info.get("file_type", "CSV"),
                    "headers": file_info.get("file_headers", []),
                    "sample_data": cleaned_sample_data,
                    "column_count": len(file_info.get("file_headers", [])),
                    "row_count": len(file_info.get("sample_data", []))
                })
            
            # Create comprehensive batch schema interpretation prompt
            batch_prompt = f"""You are a financial data schema interpretation specialist processing ALL {len(files_data)} uploaded files in a SINGLE response.

FILES TO ANALYZE (Process ALL in one response):
{json.dumps(file_analysis_data, indent=2)}

CRITICAL BATCH PROCESSING RULES:
1. Process ALL {len(files_data)} files in this single AI response
2. For each file, identify the transaction schema mapping
3. Map columns to standard transaction fields: date, description, amount, account, reference, etc.
4. Detect debit/credit patterns and amount handling
5. Identify bank format or accounting software format if recognizable

REQUIRED TRANSACTION FIELD MAPPINGS:
- date: Transaction date column
- description: Transaction description/memo column  
- amount: Transaction amount column (or separate debit/credit columns)
- account: Account identifier column (optional)
- reference: Transaction reference/ID column (optional)
- balance: Account balance column (optional)

COMMON PATTERNS TO DETECT:
- Bank statements: Date, Description, Debit, Credit, Balance
- Accounting exports: Date, Account, Description, Amount, Category
- Credit card statements: Date, Merchant, Amount, Reference
- Excel templates: Various custom formats with transaction data

RESPONSE FORMAT (JSON only):
{{
  "file_interpretations": [
    {{
      "index": 0,
      "file_name": "bank_statement.csv",
      "confidence": 0.95,
      "column_mappings": [
        {{"source_column": "Date", "target_field": "date", "confidence": 0.98}},
        {{"source_column": "Description", "target_field": "description", "confidence": 0.95}},
        {{"source_column": "Amount", "target_field": "amount", "confidence": 0.90}}
      ],
      "detected_format": "Bank Statement",
      "debit_credit_logic": "Single amount column with negative values for debits",
      "recommendations": ["Standard bank format - ready for processing"],
      "issues": []
    }},
    // ... continue for all {len(files_data)} files
  ],
  "batch_summary": {{
    "total_files_processed": {len(files_data)},
    "successful_interpretations": 0,
    "common_patterns_detected": [],
    "overall_confidence": 0.0
  }}
}}

Process all files now:"""

            # Make single AI call for all files
            logger.info(f"Making SINGLE AI call for {len(files_data)} file schema interpretations")
            
            generation_config = {
                "temperature": 0.2,
                "max_output_tokens": 8192,
                "response_mime_type": "application/json"
            }
            
            ai_response = await safe_schema_ai_call(batch_prompt, generation_config)
            
            # Parse AI response
            try:
                result_data = json.loads(ai_response)
                file_interpretations = result_data.get("file_interpretations", [])
                
                # Validate we got results for all files
                if len(file_interpretations) != len(files_data):
                    logger.warning(f"AI returned {len(file_interpretations)} results for {len(files_data)} files")
                
                # Format results for compatibility
                batch_results = {
                    "files_processed": len(files_data),
                    "interpretations": [],
                    "batch_summary": result_data.get("batch_summary", {}),
                    "success": True
                }
                
                for i, file_info in enumerate(files_data):
                    if i < len(file_interpretations):
                        interpretation = file_interpretations[i]
                        
                        # Format single file result
                        file_result = {
                            "file_name": interpretation.get("file_name", file_info.get("file_name")),
                            "overall_confidence": interpretation.get("confidence", 0.0),
                            "column_mappings": interpretation.get("column_mappings", []),
                            "detected_format": interpretation.get("detected_format", "Unknown"),
                            "debit_credit_logic": interpretation.get("debit_credit_logic", ""),
                            "recommendations": interpretation.get("recommendations", []),
                            "issues": interpretation.get("issues", []),
                            "success": True
                        }
                        
                        batch_results["interpretations"].append(file_result)
                    else:
                        # Missing result for this file
                        batch_results["interpretations"].append({
                            "file_name": file_info.get("file_name", f"file_{i}"),
                            "overall_confidence": 0.0,
                            "column_mappings": [],
                            "detected_format": "Failed",
                            "error": "No AI result for this file",
                            "success": False
                        })
                
                logger.info(f"TRUE BATCH SCHEMA SUCCESS: Processed {len(batch_results['interpretations'])} files in ONE call")
                return batch_results
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse AI response as JSON: {e}")
                logger.error(f"AI response: {ai_response[:500]}")
                return {
                    "files_processed": len(files_data),
                    "interpretations": [],
                    "batch_summary": {"error": f"Invalid AI response: {e}"},
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"TRUE BATCH SCHEMA INTERPRETATION FAILED: {e}")
            return {
                "files_processed": len(files_data),
                "interpretations": [],
                "batch_summary": {"error": f"Batch processing failed: {e}"},
                "success": False
            }

    async def infer_debit_credit_logic(
        self, transaction_samples: list[dict[str, Any]], column_mappings: dict[str, str]
    ) -> dict[str, Any]:
        """
        Smart debit/credit detection using accounting principles.

        Args:
            transaction_samples: Sample transaction data
            column_mappings: Mapped column names to transaction fields

        Returns:
            Dictionary with debit/credit inference results
        """
        return await infer_debit_credit_logic_tool_function(
            transaction_samples=transaction_samples,
            column_mappings=column_mappings,
        )

    async def map_to_unified_schema(
        self,
        original_schema: dict[str, str],
        debit_credit_info: dict[str, Any],
        regional_info: dict[str, str] | None = None,
    ) -> dict[str, Any]:
        """
        Convert diverse formats to standardized transaction schema.

        Args:
            original_schema: Original column mappings
            debit_credit_info: Debit/credit inference results
            regional_info: Optional regional banking information

        Returns:
            Dictionary with unified schema mapping
        """
        return await map_to_unified_schema_tool_function(
            original_schema=original_schema,
            debit_credit_info=debit_credit_info,
            regional_info=regional_info,
        )

    async def handle_regional_variations(
        self, file_data: dict[str, Any], detected_region: str | None = None
    ) -> dict[str, Any]:
        """
        Support regional banking terminology and conventions.

        Args:
            file_data: File metadata and sample data
            detected_region: Optional detected region/country

        Returns:
            Dictionary with regional handling instructions
        """
        return await handle_regional_variations_tool_function(
            file_data=file_data, detected_region=detected_region
        )

    async def validate_schema_mapping(
        self, schema_mapping: dict[str, Any], sample_data: list[dict[str, Any]]
    ) -> dict[str, Any]:
        """
        Ensure mapping accuracy before database storage.

        Args:
            schema_mapping: Proposed schema mapping
            sample_data: Sample transaction data for validation

        Returns:
            Dictionary with validation results and recommendations
        """
        return await validate_schema_mapping_tool_function(
            schema_mapping=schema_mapping, sample_data=sample_data
        )

    async def suggest_column_corrections(
        self,
        original_mappings: dict[str, str],
        validation_errors: list[str],
        user_feedback: dict[str, Any] | None = None,
    ) -> dict[str, Any]:
        """
        Enable customer corrections when schema interpretation needs adjustment.

        Args:
            original_mappings: Original column mappings
            validation_errors: List of validation errors
            user_feedback: Optional user feedback on mappings

        Returns:
            Dictionary with correction suggestions
        """
        return await suggest_column_corrections_tool_function(
            original_mappings=original_mappings,
            validation_errors=validation_errors,
            user_feedback=user_feedback,
        )

    async def analyze_categorization_patterns(
        self, categorization_columns: list[str], sample_categories: dict[str, list[str]]
    ) -> dict[str, Any]:
        """
        Analyze customer categorization patterns and hierarchies.

        Args:
            categorization_columns: List of column names containing categories
            sample_categories: Sample category values for each column

        Returns:
            Dictionary with categorization analysis
        """
        logger.info(
            f"Analyzing categorization patterns for columns: {categorization_columns}"
        )

        result = await detect_category_hierarchy_tool_function(
            categorization_columns=categorization_columns,
            sample_categories=sample_categories,
        )

        logger.info(
            f"Categorization analysis complete: {len(result['categorization_patterns'])} patterns detected"
        )
        return result
