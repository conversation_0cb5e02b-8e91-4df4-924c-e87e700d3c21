"""
Unified Files Router
====================

Consolidated files router using the UnifiedBaseRouter system.
Migrates from the original files/router.py with standardized patterns.

This router consolidates:
- File upload operations
- File processing workflows
- Schema interpretation
- Column mapping management
- Processing status tracking
- File validation and analysis
"""

import logging

from asyncpg import Connection
from fastapi import APIRouter, Depends, File, HTTPException, Query, UploadFile, status
from pydantic import BaseModel

from ...core.dependencies import get_current_tenant_id, get_db_session
from ...shared.routers.base_router import (
    BaseRouterConfig,
    StandardResponse,
    UnifiedBaseRouter,
)
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .schemas import (
    ColumnMapping,
)
from .service import FileService

logger = logging.getLogger(__name__)


class FileUploadParams(BaseModel):
    """Parameters for file upload operations."""
    auto_process: bool = Query(False, description="Automatically process after upload")
    skip_validation: bool = Query(False, description="Skip file validation")
    currency: str | None = Query("USD", description="Default currency")


class ProcessingParams(BaseModel):
    """Parameters for file processing operations."""
    force_reprocess: bool = Query(False, description="Force reprocessing even if already processed")
    include_debug: bool = Query(False, description="Include debug information")
    ai_interpretation: bool = Query(True, description="Use AI for schema interpretation")


class UnifiedFilesRouter(UnifiedBaseRouter):
    """
    Unified files router providing standardized file management functionality.
    
    Features:
    - File upload and validation
    - AI-powered schema interpretation
    - Column mapping management
    - Processing workflow orchestration
    - Progress tracking and status monitoring
    """
    
    def __init__(self):
        config = BaseRouterConfig(
            prefix="/api/v1/files",  # Fixed: Changed back to avoid conflicts with other routers
            tags=["Files"],
            include_auth=True,
            include_tenant_isolation=True,
            include_caching=False,  # File operations shouldn't be cached
            include_performance_monitoring=True,
        )
        super().__init__(config)
    
    def _register_routes(self) -> None:
        """Register all file-related routes."""
        
        # Upload operations
        self._register_upload_routes()
        
        # Processing workflows
        self._register_processing_routes()
        
        # Schema and column mapping
        self._register_schema_routes()
        
        # Status and monitoring
        self._register_status_routes()
    
    def _register_upload_routes(self) -> None:
        """Register file upload routes."""
        
        @self.router.get("/upload", response_model=StandardResponse)
        async def get_upload_info(
            tenant_id: int = Depends(get_current_tenant_id),
        ):
            """Get upload information and capabilities."""
            return StandardResponse(
                success=True,
                data={
                    "supported_formats": [".csv", ".xlsx", ".xls"],
                    "max_file_size_mb": 50,
                    "upload_endpoint": "/api/v1/files/upload",
                    "status": "ready"
                },
                message="Upload service is available"
            )
        
        @self.router.post("/upload", response_model=StandardResponse)
        async def upload_file(
            file: UploadFile = File(...),
            params: FileUploadParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Upload a single file for processing."""
            try:
                service = FileService(conn, tenant_id)
                
                # Validate file
                if not params.skip_validation:
                    await service.validate_file(file)
                
                # Process upload
                upload_result = await service.process_file(
                    file=file,
                    user_id=user.id,
                    auto_process=params.auto_process,
                    currency=params.currency,
                )
                
                return StandardResponse(
                    success=True,
                    data=upload_result,
                    message=f"File {file.filename} uploaded successfully",
                    metadata={
                        "file_size": file.size,
                        "file_type": file.content_type,
                        "auto_processed": params.auto_process,
                    }
                )
                
            except Exception as e:
                logger.error(f"Error uploading file {file.filename}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to upload file: {e!s}"
                )
        
        @self.router.post("/upload/multiple", response_model=StandardResponse)
        async def upload_multiple_files(
            files: list[UploadFile] = File(...),
            params: FileUploadParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Upload multiple files for batch processing."""
            try:
                service = FileService(conn, tenant_id)
                
                # Validate all files first
                if not params.skip_validation:
                    for file in files:
                        await service.validate_file(file)
                
                # Process uploads
                upload_results = []
                for file in files:
                    try:
                        result = await service.process_file(
                            file=file,
                            user_id=user.id,
                            auto_process=params.auto_process,
                            currency=params.currency,
                        )
                        upload_results.append(result)
                    except Exception as e:
                        logger.error(f"Error processing file {file.filename}: {e}")
                        upload_results.append({
                            "filename": file.filename,
                            "success": False,
                            "error": str(e)
                        })
                
                successful_uploads = sum(1 for r in upload_results if r.get("success", True))
                
                return StandardResponse(
                    success=True,
                    data={
                        "uploads": upload_results,
                        "total_files": len(files),
                        "successful_uploads": successful_uploads,
                        "failed_uploads": len(files) - successful_uploads,
                    },
                    message=f"Processed {len(files)} files, {successful_uploads} successful"
                )
                
            except Exception as e:
                logger.error(f"Error in bulk upload: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to process multiple files: {e!s}"
                )
        
        @self.router.post("/upload/production", response_model=StandardResponse)
        async def upload_production_file(
            file: UploadFile = File(...),
            params: FileUploadParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Upload file for production processing with enhanced validation."""
            try:
                service = FileService(conn, tenant_id)
                
                # Enhanced validation for production
                await service.validate_production_file(file)
                
                # Process with production settings
                upload_result = await service.process_production_file(
                    file=file,
                    user_id=user.id,
                    currency=params.currency,
                )
                
                return StandardResponse(
                    success=True,
                    data=upload_result,
                    message=f"Production file {file.filename} uploaded successfully"
                )
                
            except Exception as e:
                logger.error(f"Error uploading production file {file.filename}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to upload production file: {e!s}"
                )
    
    def _register_processing_routes(self) -> None:
        """Register file processing routes."""
        
        @self.router.post("/process/{upload_id}", response_model=StandardResponse)
        async def process_uploaded_file(
            upload_id: str,
            params: ProcessingParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Process an uploaded file with column mapping."""
            try:
                service = FileService(conn, tenant_id)
                
                # Check if already processed
                if not params.force_reprocess:
                    status_info = await service.get_processing_status(upload_id)
                    if status_info.get("status") == "completed":
                        return StandardResponse(
                            success=True,
                            data=status_info,
                            message="File already processed"
                        )
                
                # Start processing
                processing_result = await service.start_processing(
                    upload_id=upload_id,
                    user_id=user.id,
                    ai_interpretation=params.ai_interpretation,
                    include_debug=params.include_debug,
                )
                
                return StandardResponse(
                    success=True,
                    data=processing_result,
                    message="File processing started successfully"
                )
                
            except Exception as e:
                logger.error(f"Error processing file {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to process file: {e!s}"
                )
        
        @self.router.post("/extract/{upload_id}", response_model=StandardResponse)
        async def extract_transactions(
            upload_id: str,
            column_mapping: ColumnMapping,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Extract transactions using provided column mapping."""
            try:
                service = FileService(conn, tenant_id)
                
                # Validate column mapping
                await service.validate_column_mapping(upload_id, column_mapping)
                
                # Extract transactions
                extraction_result = await service.extract_transactions(
                    upload_id=upload_id,
                    column_mapping=column_mapping,
                    user_id=user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data=extraction_result,
                    message=f"Extracted {extraction_result.get('transaction_count', 0)} transactions"
                )
                
            except Exception as e:
                logger.error(f"Error extracting transactions from {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to extract transactions: {e!s}"
                )
        
        @self.router.post("/reprocess/{upload_id}", response_model=StandardResponse)
        async def reprocess_file(
            upload_id: str,
            new_mapping: ColumnMapping | None = None,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Reprocess a file with new settings or column mapping."""
            try:
                service = FileService(conn, tenant_id)
                
                reprocess_result = await service.reprocess_file(
                    upload_id=upload_id,
                    new_mapping=new_mapping,
                    user_id=user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data=reprocess_result,
                    message="File reprocessed successfully"
                )
                
            except Exception as e:
                logger.error(f"Error reprocessing file {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to reprocess file: {e!s}"
                )
    
    def _register_schema_routes(self) -> None:
        """Register schema interpretation and column mapping routes."""
        
        @self.router.get("/columns/{upload_id}", response_model=StandardResponse)
        async def get_file_columns(
            upload_id: str,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get column information for an uploaded file."""
            try:
                service = FileService(conn, tenant_id)
                columns = await service.get_file_columns(upload_id)
                
                if not columns:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"File {upload_id} not found or not processed"
                    )
                
                return StandardResponse(
                    success=True,
                    data=columns,
                    message="File columns retrieved successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting columns for {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve file columns: {e!s}"
                )
        
        @self.router.post("/schema-interpretation/{upload_id}", response_model=StandardResponse)
        async def get_schema_interpretation(
            upload_id: str,
            use_ai: bool = Query(True, description="Use AI for interpretation"),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get AI-powered schema interpretation for file columns."""
            try:
                service = FileService(conn, tenant_id)
                
                interpretation = await service.get_schema_interpretation(
                    upload_id=upload_id,
                    use_ai=use_ai,
                    user_id=user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data=interpretation,
                    message="Schema interpretation completed"
                )
                
            except Exception as e:
                logger.error(f"Error interpreting schema for {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to interpret schema: {e!s}"
                )
        
        @self.router.post("/validate-mapping/{upload_id}", response_model=StandardResponse)
        async def validate_column_mapping(
            upload_id: str,
            column_mapping: ColumnMapping,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Validate a column mapping against file structure."""
            try:
                service = FileService(conn, tenant_id)
                
                validation_result = await service.validate_column_mapping(
                    upload_id=upload_id,
                    column_mapping=column_mapping,
                )
                
                return StandardResponse(
                    success=True,
                    data=validation_result,
                    message="Column mapping validation completed"
                )
                
            except Exception as e:
                logger.error(f"Error validating mapping for {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to validate column mapping: {e!s}"
                )
    
    def _register_status_routes(self) -> None:
        """Register status and monitoring routes."""
        
        @self.router.get("/status/{upload_id}", response_model=StandardResponse)
        async def get_processing_status(
            upload_id: str,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get processing status for an uploaded file."""
            try:
                service = FileService(conn, tenant_id)
                status_info = await service.get_processing_status(upload_id)
                
                if not status_info:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Upload {upload_id} not found"
                    )
                
                return StandardResponse(
                    success=True,
                    data=status_info,
                    message="Processing status retrieved successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting status for {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve processing status: {e!s}"
                )
        
        @self.router.get("/uploads", response_model=StandardResponse)
        async def list_user_uploads(
            limit: int = Query(20, ge=1, le=100, description="Number of uploads to retrieve"),
            offset: int = Query(0, ge=0, description="Number of uploads to skip"),
            status_filter: str | None = Query(None, description="Filter by status"),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """List user's file uploads with status information."""
            try:
                service = FileService(conn, tenant_id)
                
                uploads = await service.list_user_uploads(
                    user_id=user.id,
                    limit=limit,
                    offset=offset,
                    status_filter=status_filter,
                )
                
                return StandardResponse(
                    success=True,
                    data=uploads,
                    message=f"Retrieved {len(uploads.get('uploads', []))} uploads"
                )
                
            except Exception as e:
                logger.error(f"Error listing uploads for user {user.id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve uploads: {e!s}"
                )
        
        @self.router.get("/supported-formats", response_model=StandardResponse)
        async def get_supported_formats(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
        ):
            """Get list of supported file formats and their specifications."""
            try:
                supported_formats = {
                    "csv": {
                        "extensions": [".csv"],
                        "max_size_mb": 50,
                        "description": "Comma-separated values",
                        "encoding": ["utf-8", "latin-1"],
                    },
                    "excel": {
                        "extensions": [".xlsx", ".xls"],
                        "max_size_mb": 100,
                        "description": "Microsoft Excel files",
                        "sheets_supported": True,
                    },
                    "pdf": {
                        "extensions": [".pdf"],
                        "max_size_mb": 25,
                        "description": "PDF bank statements (experimental)",
                        "ocr_required": True,
                    },
                }
                
                return StandardResponse(
                    success=True,
                    data=supported_formats,
                    message="Supported file formats retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting supported formats: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve supported formats: {e!s}"
                )
        
        @self.router.delete("/{upload_id}", response_model=StandardResponse)
        async def delete_upload(
            upload_id: str,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Delete an uploaded file and its associated data."""
            try:
                service = FileService(conn, tenant_id)
                
                success = await service.delete_upload(
                    upload_id=upload_id,
                    user_id=user.id,
                )
                
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Upload {upload_id} not found"
                    )
                
                return StandardResponse(
                    success=True,
                    message="Upload deleted successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error deleting upload {upload_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to delete upload: {e!s}"
                )
    
    def get_router(self) -> APIRouter:
        """Get the configured router instance."""
        return self.router


# Create module-level router instance for backward compatibility
files_router = UnifiedFilesRouter()
router = files_router.get_router()

# Export the router class for registration
__all__ = ["UnifiedFilesRouter", "router"]