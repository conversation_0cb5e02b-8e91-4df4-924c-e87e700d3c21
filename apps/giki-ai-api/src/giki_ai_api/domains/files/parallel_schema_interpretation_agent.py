"""
Parallel Schema Interpretation Agent - ADK v1.3.0 ParallelAgent Pattern
=======================================================================

This agent implements the ParallelAgent pattern for concurrent multi-file schema interpretation
with true parallel processing capabilities.

ADK Paradigm: Uses ParallelAgent for concurrent, independent file processing workflows
Parallel Steps: Multiple files processed simultaneously with result aggregation

Key capabilities:
- Concurrent processing of multiple uploaded files
- Independent schema interpretation for each file
- Parallel debit/credit logic inference
- Concurrent regional format detection
- Aggregated results with cross-file pattern analysis
- Performance optimization through true parallelism
"""

import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any

import asyncpg

logger = logging.getLogger(__name__)

# Real ADK imports
from google.adk.tools import FunctionTool, LongRunningFunctionTool

from ...shared.ai.standard_giki_agent import StandardGikiAgent

# Import the original schema interpretation functions for reuse
from .schema_interpretation_agent import (
    handle_regional_variations_tool_function,
    infer_debit_credit_logic_tool_function,
    suggest_schema_mapping_tool_function,
    validate_schema_mapping_tool_function,
)

# ===== PARALLEL SCHEMA PROCESSING TOOLS =====


async def process_single_file_schema_tool_function(
    file_data: dict[str, Any],
    file_index: int,
    tenant_id: int,
    processing_context: dict[str, Any] | None = None,
    **_kwargs,
) -> dict[str, Any]:
    """
    PARALLEL TASK: Process single file schema interpretation.
    
    This function is designed to run in parallel with other file processing tasks.
    Each file is processed independently and concurrently.
    
    Args:
        file_data: Single file data including headers and sample data
        file_index: Index of the file in the batch
        tenant_id: Tenant ID for data isolation
        processing_context: Optional processing context
    
    Returns:
        Dictionary with single file schema interpretation results
    """
    logger.info(f"PARALLEL TASK {file_index}: Processing schema for {file_data.get('file_name', f'file_{file_index}')}")
    
    try:
        start_time = datetime.now()
        
        # Extract file information
        file_name = file_data.get("file_name", f"file_{file_index}")
        file_headers = file_data.get("file_headers", [])
        sample_data = file_data.get("sample_data", [])
        column_stats = file_data.get("column_stats")
        
        # Perform schema interpretation using existing tool
        schema_result = await suggest_schema_mapping_tool_function(
            file_name=file_name,
            file_headers=file_headers,
            sample_data=sample_data,
            column_stats=column_stats,
            previous_patterns=processing_context.get("previous_patterns") if processing_context else None
        )
        
        # Extract column mappings for debit/credit analysis
        column_mappings = {}
        for mapping in schema_result.get("column_mappings", []):
            if mapping.get("confidence", 0) > 0.5:
                column_mappings[mapping["source_column"]] = mapping["target_field"]
        
        # Prepare transaction samples for debit/credit inference
        transaction_samples = []
        for row in sample_data[:5]:  # Use first 5 rows
            if len(row) >= len(file_headers):
                sample = {file_headers[i]: row[i] if i < len(row) else None for i in range(len(file_headers))}
                transaction_samples.append(sample)
        
        # Concurrent processing: Schema + Debit/Credit + Regional analysis
        debit_credit_task = None
        regional_task = None
        
        if transaction_samples and column_mappings:
            # Create async tasks for concurrent processing
            debit_credit_task = asyncio.create_task(
                infer_debit_credit_logic_tool_function(
                    transaction_samples=transaction_samples,
                    column_mappings=column_mappings
                )
            )
            
            regional_task = asyncio.create_task(
                handle_regional_variations_tool_function(
                    file_data={
                        "file_name": file_name,
                        "headers": file_headers,
                        "sample_data": sample_data[:3]  # Smaller sample for regional analysis
                    }
                )
            )
        
        # Wait for concurrent tasks to complete
        debit_credit_result = None
        regional_result = None
        
        if debit_credit_task and regional_task:
            try:
                # Wait for both tasks with timeout
                debit_credit_result, regional_result = await asyncio.wait_for(
                    asyncio.gather(debit_credit_task, regional_task, return_exceptions=True),
                    timeout=30.0  # 30 second timeout for each file
                )
                
                # Handle exceptions from parallel tasks
                if isinstance(debit_credit_result, Exception):
                    logger.warning(f"Debit/credit analysis failed for {file_name}: {debit_credit_result}")
                    debit_credit_result = {"error": str(debit_credit_result)}
                    
                if isinstance(regional_result, Exception):
                    logger.warning(f"Regional analysis failed for {file_name}: {regional_result}")
                    regional_result = {"error": str(regional_result)}
                    
            except TimeoutError:
                logger.warning(f"Parallel processing timeout for {file_name}")
                debit_credit_result = {"error": "Processing timeout"}
                regional_result = {"error": "Processing timeout"}
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Aggregate results
        parallel_result = {
            "file_index": file_index,
            "file_name": file_name,
            "schema_interpretation": schema_result,
            "debit_credit_analysis": debit_credit_result,
            "regional_analysis": regional_result,
            "processing_metadata": {
                "processing_time_seconds": processing_time,
                "parallel_tasks_completed": 3,
                "tenant_id": tenant_id,
                "processed_at": datetime.now().isoformat()
            },
            "success": True
        }
        
        # Validate schema mapping
        if column_mappings:
            try:
                validation_result = await validate_schema_mapping_tool_function(
                    schema_mapping={
                        "unified_schema": {"required_fields": column_mappings},
                        "transformation_rules": debit_credit_result or {}
                    },
                    sample_data=transaction_samples
                )
                parallel_result["validation_result"] = validation_result
            except Exception as validation_error:
                logger.warning(f"Schema validation failed for {file_name}: {validation_error}")
                parallel_result["validation_result"] = {"error": str(validation_error)}
        
        logger.info(f"✅ PARALLEL TASK {file_index}: Completed {file_name} in {processing_time:.2f}s")
        return parallel_result
        
    except Exception as e:
        logger.error(f"❌ PARALLEL TASK {file_index}: Failed to process {file_data.get('file_name', f'file_{file_index}')}: {e}")
        return {
            "file_index": file_index,
            "file_name": file_data.get("file_name", f"file_{file_index}"),
            "error": str(e),
            "success": False,
            "processing_metadata": {
                "processing_time_seconds": 0,
                "tenant_id": tenant_id,
                "failed_at": datetime.now().isoformat()
            }
        }


async def aggregate_parallel_results_tool_function(
    parallel_results: list[dict[str, Any]],
    tenant_id: int,
    **_kwargs,
) -> dict[str, Any]:
    """
    AGGREGATION STEP: Combine parallel processing results and identify cross-file patterns.
    
    Analyzes results from all parallel file processing tasks to identify:
    - Common schema patterns across files
    - Consistent debit/credit conventions
    - Regional format consistency
    - Overall batch processing quality
    
    Args:
        parallel_results: Results from all parallel file processing tasks
        tenant_id: Tenant ID for data isolation
    
    Returns:
        Dictionary with aggregated analysis and cross-file insights
    """
    logger.info(f"AGGREGATION: Analyzing results from {len(parallel_results)} parallel tasks")
    
    try:
        # Separate successful and failed results
        successful_results = [r for r in parallel_results if r.get("success", False)]
        failed_results = [r for r in parallel_results if not r.get("success", False)]
        
        if not successful_results:
            return {
                "aggregation_success": False,
                "error": "No successful file interpretations to aggregate",
                "failed_files": len(failed_results),
                "total_files": len(parallel_results)
            }
        
        # Extract patterns from successful results
        schema_patterns = []
        debit_credit_patterns = []
        regional_patterns = []
        confidence_scores = []
        processing_times = []
        
        for result in successful_results:
            schema_interp = result.get("schema_interpretation", {})
            debit_credit = result.get("debit_credit_analysis", {})
            regional = result.get("regional_analysis", {})
            metadata = result.get("processing_metadata", {})
            
            # Collect schema patterns
            if schema_interp.get("column_mappings"):
                mapped_fields = [m.get("target_field") for m in schema_interp["column_mappings"]]
                schema_patterns.append({
                    "file_name": result["file_name"],
                    "mapped_fields": mapped_fields,
                    "confidence": schema_interp.get("overall_confidence", 0),
                    "detected_format": schema_interp.get("bank_detection", {}).get("detected_bank", {}).get("bank_name", "Unknown")
                })
                confidence_scores.append(schema_interp.get("overall_confidence", 0))
            
            # Collect debit/credit patterns
            if debit_credit and not debit_credit.get("error"):
                debit_credit_patterns.append({
                    "file_name": result["file_name"],
                    "structure": debit_credit.get("debit_credit_structure"),
                    "sign_convention": debit_credit.get("sign_convention"),
                    "confidence": debit_credit.get("confidence", 0)
                })
            
            # Collect regional patterns
            if regional and not regional.get("error"):
                regional_patterns.append({
                    "file_name": result["file_name"],
                    "detected_region": regional.get("detected_region"),
                    "date_format": regional.get("date_format"),
                    "currency": regional.get("currency", {}).get("symbol"),
                    "confidence": regional.get("confidence", 0)
                })
            
            # Collect performance metrics
            processing_times.append(metadata.get("processing_time_seconds", 0))
        
        # Analyze cross-file patterns
        cross_file_analysis = await _analyze_cross_file_patterns(
            schema_patterns, debit_credit_patterns, regional_patterns
        )
        
        # Calculate performance metrics
        total_processing_time = sum(processing_times)
        avg_processing_time = total_processing_time / len(processing_times) if processing_times else 0
        max_processing_time = max(processing_times) if processing_times else 0
        
        # Performance improvement calculation
        # Sequential processing would be sum of all times
        # Parallel processing is max time (longest running task)
        performance_improvement = ((total_processing_time - max_processing_time) / total_processing_time * 100) if total_processing_time > 0 else 0
        
        # Build aggregated result
        aggregated_result = {
            "aggregation_success": True,
            "tenant_id": tenant_id,
            "batch_summary": {
                "total_files": len(parallel_results),
                "successful_files": len(successful_results),
                "failed_files": len(failed_results),
                "average_confidence": sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
                "batch_confidence": "high" if len(successful_results) / len(parallel_results) > 0.8 else "medium"
            },
            "cross_file_patterns": cross_file_analysis,
            "performance_metrics": {
                "total_sequential_time": total_processing_time,
                "actual_parallel_time": max_processing_time,
                "performance_improvement_percent": performance_improvement,
                "average_file_processing_time": avg_processing_time,
                "parallelism_efficiency": len(successful_results) / max_processing_time if max_processing_time > 0 else 0
            },
            "file_results": parallel_results,
            "recommendations": _generate_batch_recommendations(
                successful_results, failed_results, cross_file_analysis
            ),
            "aggregated_at": datetime.now().isoformat()
        }
        
        logger.info(f"✅ AGGREGATION: Completed analysis - {len(successful_results)}/{len(parallel_results)} files successful, {performance_improvement:.1f}% performance improvement")
        return aggregated_result
        
    except Exception as e:
        logger.error(f"❌ AGGREGATION: Failed to aggregate parallel results: {e}")
        return {
            "aggregation_success": False,
            "error": f"Aggregation failed: {e}",
            "total_files": len(parallel_results),
            "failed_aggregation_at": datetime.now().isoformat()
        }


async def optimize_parallel_processing_tool_function(
    files_data: list[dict[str, Any]],
    tenant_id: int,
    max_concurrent_files: int = 5,
    **_kwargs,
) -> dict[str, Any]:
    """
    OPTIMIZATION STEP: Optimize parallel processing based on file characteristics.
    
    Analyzes file batch to determine optimal parallel processing strategy:
    - Group similar files for batch processing
    - Determine optimal concurrency levels
    - Pre-process large files vs small files
    - Memory and resource optimization
    
    Args:
        files_data: List of files to be processed
        tenant_id: Tenant ID for data isolation
        max_concurrent_files: Maximum number of files to process concurrently
    
    Returns:
        Dictionary with optimization strategy and processing plan
    """
    logger.info(f"OPTIMIZATION: Analyzing {len(files_data)} files for parallel processing strategy")
    
    try:
        # Analyze file characteristics
        file_characteristics = []
        total_data_size = 0
        
        for i, file_data in enumerate(files_data):
            headers = file_data.get("file_headers", [])
            sample_data = file_data.get("sample_data", [])
            
            characteristics = {
                "index": i,
                "file_name": file_data.get("file_name", f"file_{i}"),
                "column_count": len(headers),
                "sample_row_count": len(sample_data),
                "estimated_complexity": len(headers) * len(sample_data),
                "file_type": file_data.get("file_type", "unknown")
            }
            
            file_characteristics.append(characteristics)
            total_data_size += characteristics["estimated_complexity"]
        
        # Determine processing strategy
        avg_complexity = total_data_size / len(files_data) if files_data else 0
        
        # Group files by complexity
        simple_files = [f for f in file_characteristics if f["estimated_complexity"] <= avg_complexity * 0.5]
        medium_files = [f for f in file_characteristics if avg_complexity * 0.5 < f["estimated_complexity"] <= avg_complexity * 2]
        complex_files = [f for f in file_characteristics if f["estimated_complexity"] > avg_complexity * 2]
        
        # Determine optimal concurrency
        if len(files_data) <= 3:
            optimal_concurrency = len(files_data)  # Process all files concurrently
        elif len(complex_files) > 0:
            optimal_concurrency = min(max_concurrent_files, 3)  # Conservative for complex files
        else:
            optimal_concurrency = min(max_concurrent_files, len(files_data))
        
        # Create processing batches
        processing_batches = []
        remaining_files = file_characteristics.copy()
        
        while remaining_files:
            # Take up to optimal_concurrency files for this batch
            batch = remaining_files[:optimal_concurrency]
            remaining_files = remaining_files[optimal_concurrency:]
            processing_batches.append(batch)
        
        optimization_result = {
            "optimization_success": True,
            "tenant_id": tenant_id,
            "file_analysis": {
                "total_files": len(files_data),
                "simple_files": len(simple_files),
                "medium_files": len(medium_files),
                "complex_files": len(complex_files),
                "total_estimated_complexity": total_data_size,
                "average_complexity": avg_complexity
            },
            "processing_strategy": {
                "optimal_concurrency": optimal_concurrency,
                "total_batches": len(processing_batches),
                "estimated_total_time": max(sum(f["estimated_complexity"] for f in batch) / 1000 for batch in processing_batches) if processing_batches else 0,
                "memory_optimization": "enabled" if len(complex_files) > 0 else "standard"
            },
            "processing_batches": processing_batches,
            "recommendations": [
                f"Process {len(files_data)} files in {len(processing_batches)} parallel batches",
                f"Optimal concurrency: {optimal_concurrency} files per batch",
                f"Estimated performance improvement: {((len(files_data) - optimal_concurrency) / len(files_data) * 100):.1f}%" if len(files_data) > optimal_concurrency else "No improvement needed"
            ],
            "optimized_at": datetime.now().isoformat()
        }
        
        logger.info(f"✅ OPTIMIZATION: Strategy complete - {len(processing_batches)} batches, {optimal_concurrency} max concurrency")
        return optimization_result
        
    except Exception as e:
        logger.error(f"❌ OPTIMIZATION: Failed to optimize parallel processing: {e}")
        return {
            "optimization_success": False,
            "error": f"Optimization failed: {e}",
            "tenant_id": tenant_id
        }


# Helper functions for cross-file analysis

async def _analyze_cross_file_patterns(
    schema_patterns: list[dict],
    debit_credit_patterns: list[dict],
    regional_patterns: list[dict]
) -> dict[str, Any]:
    """Analyze patterns across multiple files to identify consistency and commonalities."""
    
    analysis = {
        "schema_consistency": {},
        "debit_credit_consistency": {},
        "regional_consistency": {},
        "common_patterns": [],
        "inconsistencies": []
    }
    
    # Analyze schema consistency
    if schema_patterns:
        all_fields = []
        bank_formats = []
        
        for pattern in schema_patterns:
            all_fields.extend(pattern.get("mapped_fields", []))
            bank_formats.append(pattern.get("detected_format", "Unknown"))
        
        field_frequency = {}
        for field in all_fields:
            field_frequency[field] = field_frequency.get(field, 0) + 1
        
        # Null check to prevent NoneType error
        if field_frequency is None:
            field_frequency = {}
        most_common_fields = sorted(field_frequency.items(), key=lambda x: x[1], reverse=True)[:5]
        
        analysis["schema_consistency"] = {
            "common_fields": [field for field, count in most_common_fields],
            "field_coverage": len(set(all_fields)),
            "detected_bank_formats": list(set(bank_formats)),
            "format_consistency": len(set(bank_formats)) == 1
        }
    
    # Analyze debit/credit consistency
    if debit_credit_patterns:
        structures = [p.get("structure") for p in debit_credit_patterns]
        conventions = [p.get("sign_convention") for p in debit_credit_patterns]
        
        analysis["debit_credit_consistency"] = {
            "consistent_structure": len(set(structures)) == 1,
            "most_common_structure": max(set(structures), key=structures.count) if structures else None,
            "consistent_convention": len(set(conventions)) == 1,
            "most_common_convention": max(set(conventions), key=conventions.count) if conventions else None
        }
    
    # Analyze regional consistency
    if regional_patterns:
        regions = [p.get("detected_region") for p in regional_patterns]
        date_formats = [p.get("date_format") for p in regional_patterns]
        currencies = [p.get("currency") for p in regional_patterns]
        
        analysis["regional_consistency"] = {
            "consistent_region": len(set(regions)) == 1,
            "detected_region": max(set(regions), key=regions.count) if regions else None,
            "consistent_date_format": len(set(date_formats)) == 1,
            "common_date_format": max(set(date_formats), key=date_formats.count) if date_formats else None,
            "consistent_currency": len(set(currencies)) == 1,
            "detected_currency": max(set(currencies), key=currencies.count) if currencies else None
        }
    
    # Identify common patterns
    if analysis["schema_consistency"].get("format_consistency"):
        analysis["common_patterns"].append("All files use the same bank format")
    
    if analysis["debit_credit_consistency"].get("consistent_structure"):
        analysis["common_patterns"].append("Consistent debit/credit structure across all files")
    
    if analysis["regional_consistency"].get("consistent_region"):
        analysis["common_patterns"].append("All files from the same geographic region")
    
    # Identify inconsistencies
    if not analysis["schema_consistency"].get("format_consistency"):
        analysis["inconsistencies"].append("Mixed bank formats detected - may need different processing approaches")
    
    if not analysis["debit_credit_consistency"].get("consistent_structure"):
        analysis["inconsistencies"].append("Inconsistent debit/credit handling - manual review recommended")
    
    if not analysis["regional_consistency"].get("consistent_region"):
        analysis["inconsistencies"].append("Multiple regional formats - date/currency parsing may vary")
    
    return analysis


def _generate_batch_recommendations(
    successful_results: list[dict],
    failed_results: list[dict],
    cross_file_analysis: dict
) -> list[str]:
    """Generate actionable recommendations based on batch processing results."""
    
    recommendations = []
    
    # Success rate recommendations
    success_rate = len(successful_results) / (len(successful_results) + len(failed_results))
    
    if success_rate >= 0.9:
        recommendations.append("Excellent batch processing results - all files ready for import")
    elif success_rate >= 0.7:
        recommendations.append("Good batch processing results - review failed files for manual correction")
    else:
        recommendations.append("Mixed batch results - consider uploading files separately for better accuracy")
    
    # Pattern consistency recommendations
    if cross_file_analysis.get("common_patterns"):
        recommendations.append(f"Detected consistent patterns: {', '.join(cross_file_analysis['common_patterns'])}")
    
    if cross_file_analysis.get("inconsistencies"):
        for inconsistency in cross_file_analysis["inconsistencies"]:
            recommendations.append(f"⚠️ {inconsistency}")
    
    # Processing optimization recommendations
    if len(successful_results) > 3:
        recommendations.append("Large batch processed successfully - parallel processing provided significant time savings")
    
    # Failed file recommendations
    if failed_results:
        failed_files = [r.get("file_name", "unknown") for r in failed_results]
        recommendations.append(f"Review failed files: {', '.join(failed_files[:3])}{'...' if len(failed_files) > 3 else ''}")
    
    return recommendations


# Create FunctionTool instances for ADK integration
process_single_file_schema_tool = LongRunningFunctionTool(func=process_single_file_schema_tool_function)
aggregate_parallel_results_tool = FunctionTool(func=aggregate_parallel_results_tool_function)
optimize_parallel_processing_tool = FunctionTool(func=optimize_parallel_processing_tool_function)


@dataclass
class ParallelSchemaInterpretationAgentConfig:
    """Configuration for ParallelSchemaInterpretationAgent."""
    model_name: str = "gemini-2.0-flash-001"
    project: str | None = None
    location: str | None = None
    max_concurrent_files: int = 5
    enable_optimization: bool = True


class ParallelSchemaInterpretationAgent(StandardGikiAgent):
    """
    Parallel Schema Interpretation Agent implementing ParallelAgent pattern for multi-file processing.
    
    This agent orchestrates concurrent schema interpretation processes:
    1. Processing Optimization & Batch Strategy Planning
    2. Parallel File Schema Interpretation (Multiple Concurrent Tasks)
    3. Results Aggregation & Cross-File Pattern Analysis
    
    ADK Pattern: Implements ParallelAgent workflow through StandardGikiAgent
    Parallel Logic: Multiple files processed concurrently with optimized resource utilization
    """
    
    def __init__(
        self,
        config: ParallelSchemaInterpretationAgentConfig | None = None,
        db: asyncpg.Connection | None = None,
        **kwargs
    ):
        """Initialize the ParallelSchemaInterpretationAgent using ParallelAgent pattern."""
        if config is None:
            config = ParallelSchemaInterpretationAgentConfig(
                model_name=kwargs.get("model_name", "gemini-2.0-flash-001"),
                project=kwargs.get("project"),
                location=kwargs.get("location"),
                max_concurrent_files=kwargs.get("max_concurrent_files", 5)
            )
        
        # Store config and extract values
        self._config = config
        self._db = db
        
        # PARALLEL AGENT PATTERN: Tools for concurrent processing workflow
        parallel_tools = [
            optimize_parallel_processing_tool,    # Step 1: Optimization
            process_single_file_schema_tool,      # Step 2: Parallel processing (multiple concurrent)
            aggregate_parallel_results_tool       # Step 3: Aggregation
        ]
        
        # Initialize StandardGikiAgent with ParallelAgent-style configuration
        super().__init__(
            name="parallel_schema_interpretation_agent",
            description="Parallel multi-file schema interpretation agent",
            custom_tools=parallel_tools,
            enable_interactive_tools=False,  # Schema interpretation is automated
            enable_standard_tools=False,     # Only use custom parallel tools
            enable_code_execution=False,     # No code execution needed
            model_name=config.model_name,
            instruction="""You are a parallel processing specialist for financial file schema interpretation.
            You process multiple uploaded files concurrently through optimized parallel workflows:
            
            1. OPTIMIZE_PARALLEL_PROCESSING - Analyze files and determine optimal processing strategy
            2. PROCESS_SINGLE_FILE_SCHEMA - Execute concurrent schema interpretation for all files
            3. AGGREGATE_PARALLEL_RESULTS - Combine results and identify cross-file patterns
            
            Execute parallel tasks concurrently for maximum efficiency.
            Ensure all files are processed with high accuracy and consistency.""",
            project_id=config.project,
            location=config.location or "global",
            **kwargs
        )
        
        logger.info(f"ParallelSchemaInterpretationAgent initialized with ParallelAgent workflow pattern (max {config.max_concurrent_files} concurrent files)")
    
    async def process_multiple_files_parallel(
        self,
        files_data: list[dict[str, Any]],
        tenant_id: int,
        processing_context: dict[str, Any] | None = None
    ) -> dict[str, Any]:
        """
        Process multiple files in parallel using true ParallelAgent pattern.
        
        This is the main entry point for parallel multi-file schema interpretation.
        Files are processed concurrently for maximum efficiency.
        
        Args:
            files_data: List of file data dictionaries
            tenant_id: Tenant ID for data isolation
            processing_context: Optional processing context and preferences
        
        Returns:
            Complete parallel processing result with all file interpretations
        """
        logger.info(f"Starting parallel schema interpretation for {len(files_data)} files (tenant {tenant_id})")
        
        if not files_data:
            return {
                "success": False,
                "error": "No files provided for processing",
                "files_processed": 0
            }
        
        try:
            # Prepare workflow context
            workflow_context = {
                "tenant_id": tenant_id,
                "files_data": files_data,
                "processing_context": processing_context or {},
                "max_concurrent_files": self._config.max_concurrent_files,
                "started_at": datetime.now().isoformat()
            }
            
            # Execute parallel workflow through ADK ParallelAgent
            # In full ADK implementation, this would be:
            # result = await self.execute_parallel_workflow(workflow_context)
            
            # For now, simulate parallel execution:
            
            # Step 1: Optimize parallel processing strategy
            optimization_result = await optimize_parallel_processing_tool_function(
                files_data=files_data,
                tenant_id=tenant_id,
                max_concurrent_files=self._config.max_concurrent_files
            )
            
            if not optimization_result.get("optimization_success"):
                return {
                    "success": False,
                    "error": f"Processing optimization failed: {optimization_result.get('error')}",
                    "step_failed": "optimize_parallel_processing",
                    "details": optimization_result
                }
            
            # Step 2: Execute parallel file processing
            processing_batches = optimization_result.get("processing_batches", [])
            all_parallel_results = []
            
            total_batch_time = 0
            
            for batch_index, batch in enumerate(processing_batches):
                logger.info(f"Processing batch {batch_index + 1}/{len(processing_batches)} with {len(batch)} files")
                
                batch_start_time = datetime.now()
                
                # Create parallel tasks for this batch
                batch_tasks = []
                for file_info in batch:
                    file_data = files_data[file_info["index"]]
                    task = asyncio.create_task(
                        process_single_file_schema_tool_function(
                            file_data=file_data,
                            file_index=file_info["index"],
                            tenant_id=tenant_id,
                            processing_context=processing_context
                        )
                    )
                    batch_tasks.append(task)
                
                # Execute parallel tasks with timeout
                try:
                    batch_results = await asyncio.wait_for(
                        asyncio.gather(*batch_tasks, return_exceptions=True),
                        timeout=60.0  # 60 second timeout per batch
                    )
                    
                    # Handle any exceptions from parallel tasks
                    for i, result in enumerate(batch_results):
                        if isinstance(result, Exception):
                            logger.error(f"Parallel task {i} failed: {result}")
                            batch_results[i] = {
                                "file_index": batch[i]["index"],
                                "file_name": batch[i]["file_name"],
                                "error": str(result),
                                "success": False
                            }
                    
                    all_parallel_results.extend(batch_results)
                    
                except TimeoutError:
                    logger.error(f"Batch {batch_index + 1} timed out")
                    # Add timeout results for all files in this batch
                    for file_info in batch:
                        all_parallel_results.append({
                            "file_index": file_info["index"],
                            "file_name": file_info["file_name"],
                            "error": "Batch processing timeout",
                            "success": False
                        })
                
                batch_time = (datetime.now() - batch_start_time).total_seconds()
                total_batch_time += batch_time
                logger.info(f"Batch {batch_index + 1} completed in {batch_time:.2f}s")
            
            # Step 3: Aggregate parallel results
            aggregation_result = await aggregate_parallel_results_tool_function(
                parallel_results=all_parallel_results,
                tenant_id=tenant_id
            )
            
            if not aggregation_result.get("aggregation_success"):
                return {
                    "success": False,
                    "error": f"Results aggregation failed: {aggregation_result.get('error')}",
                    "step_failed": "aggregate_parallel_results",
                    "details": aggregation_result
                }
            
            # Successful parallel workflow completion
            final_result = {
                "success": True,
                "parallel_processing_complete": True,
                "files_processed": len(files_data),
                "optimization_strategy": optimization_result,
                "parallel_results": all_parallel_results,
                "aggregated_analysis": aggregation_result,
                "workflow_summary": {
                    "total_files": len(files_data),
                    "successful_files": len([r for r in all_parallel_results if r.get("success")]),
                    "failed_files": len([r for r in all_parallel_results if not r.get("success")]),
                    "total_processing_time": total_batch_time,
                    "average_file_time": total_batch_time / len(files_data) if files_data else 0,
                    "parallel_efficiency": aggregation_result.get("performance_metrics", {}).get("performance_improvement_percent", 0),
                    "batches_processed": len(processing_batches)
                },
                "recommendations": aggregation_result.get("recommendations", [])
            }
            
            success_rate = final_result["workflow_summary"]["successful_files"] / len(files_data)
            logger.info(f"✅ Parallel schema interpretation completed: {final_result['workflow_summary']['successful_files']}/{len(files_data)} files successful ({success_rate:.1%})")
            return final_result
            
        except Exception as e:
            logger.error(f"Parallel schema interpretation workflow failed: {e}")
            return {
                "success": False,
                "error": f"Parallel workflow execution failed: {e}",
                "step_failed": "workflow_execution",
                "files_processed": 0
            }
    
    async def get_processing_capabilities(self) -> dict[str, Any]:
        """Get parallel processing capabilities and limits."""
        return {
            "max_concurrent_files": self._config.max_concurrent_files,
            "supported_file_types": ["CSV", "Excel", "TSV"],
            "parallel_features": [
                "concurrent_schema_interpretation",
                "parallel_debit_credit_analysis", 
                "concurrent_regional_detection",
                "batch_optimization",
                "cross_file_pattern_analysis"
            ],
            "performance_optimizations": [
                "file_complexity_analysis",
                "adaptive_batch_sizing",
                "memory_optimization",
                "timeout_protection",
                "error_isolation"
            ],
            "recommended_batch_sizes": {
                "simple_files": 10,
                "medium_files": 5,
                "complex_files": 3
            }
        }
    
    async def validate_parallel_processing_readiness(
        self,
        files_data: list[dict[str, Any]],
        tenant_id: int
    ) -> dict[str, Any]:
        """Validate that files are ready for parallel processing."""
        return await optimize_parallel_processing_tool_function(
            files_data=files_data,
            tenant_id=tenant_id,
            max_concurrent_files=self._config.max_concurrent_files
        )