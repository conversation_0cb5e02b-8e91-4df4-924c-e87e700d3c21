import uuid
from typing import Any

from pydantic import BaseModel, Field


class UploadResponse(BaseModel):
    """
    Response after a file is successfully uploaded.
    """

    upload_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        description="Unique identifier for the upload process.",
    )
    filename: str = Field(..., description="Name of the uploaded file.")
    content_type: str | None = Field(
        None, description="MIME type of the uploaded file."
    )
    size: int = Field(..., description="Size of the uploaded file in bytes.")
    status: str = Field(
        default="PENDING",
        description="Current status of the upload (e.g., PENDING, PROCESSING, COMPLETED, FAILED).",
    )
    message: str | None = Field(
        None, description="A message providing more details about the upload status."
    )
    headers: list[str] | None = Field(
        None, description="Detected column headers from the file."
    )
    created_at: str | None = Field(
        None, description="Timestamp when the file was uploaded."
    )


class MultipleUploadResponse(BaseModel):
    """
    Response after multiple files are successfully uploaded.
    """

    uploads: list[UploadResponse] = Field(
        ..., description="List of upload responses for each file."
    )
    total_files: int = Field(..., description="Total number of files uploaded.")
    successful: int = Field(..., description="Number of successfully uploaded files.")
    failed: int = Field(..., description="Number of failed uploads.")
    message: str = Field(..., description="Overall upload status message.")


class ColumnListResponse(BaseModel):
    """
    Response containing the list of column headers from an uploaded file.
    """

    upload_id: str = Field(..., description="Unique identifier for the upload process.")
    columns: list[str] = Field(
        ..., description="List of column headers extracted from the file."
    )


class ColumnMapping(BaseModel):
    """
    Schema mapping for a single column.
    """

    original_name: str = Field(..., description="Original column name from the file")
    mapped_field: str = Field(
        ..., description="Target schema field (e.g., 'date', 'description', 'amount')"
    )
    confidence: float = Field(..., description="Confidence score (0.0 to 1.0)")
    reasoning: str = Field(
        ..., description="Human-readable explanation for the mapping"
    )


class SchemaInterpretationResponse(BaseModel):
    """
    Response containing intelligent schema mapping interpretation.
    """

    upload_id: str = Field(..., description="Unique identifier for the upload process.")
    filename: str = Field(..., description="Name of the uploaded file.")
    column_mappings: list[ColumnMapping] = Field(
        ..., description="Intelligent column mappings"
    )
    overall_confidence: float = Field(
        ..., description="Overall confidence in the interpretation"
    )
    required_fields_mapped: dict[str, bool] = Field(
        ..., description="Status of required field mappings"
    )
    interpretation_summary: str = Field(
        ..., description="Human-readable summary of the interpretation"
    )
    debit_credit_inference: dict[str, Any] | None = Field(
        default={}, description="AI inference about debit/credit column usage"
    )
    regional_variations: list[str] | None = Field(
        default=[], description="Detected regional banking terminology variations"
    )


class ColumnMappingPayload(BaseModel):  # Renamed from ColumnMappingRequest
    """
    Payload for submitting column mappings.
    Keys are source column names from the uploaded file, values are target internal field names.
    Enhanced to support AI interpretation when mapping not provided.
    """

    mapping: dict[str, str | None] | None = Field(
        None,
        description="Dictionary mapping source column names to target field names. If not provided, AI interpretation will be used.",
    )


class ProcessedFileResponse(BaseModel):
    """
    Response after a file has been processed based on column mappings.
    """

    message: str = Field(
        ...,
        description="A message providing more details about the processing outcome.",
    )
    records_processed: int = Field(
        ..., description="Number of records successfully processed from the file."
    )
    errors: list[str] = Field(
        default_factory=list,
        description="List of error messages encountered during processing.",
    )
    upload_id: str = Field(
        ...,
        description="Unique identifier for the upload process this result pertains to.",
    )
    categorization_job_id: str | None = Field(
        None, description="ID of the background job for AI categorization, if started."
    )
    report_id: str | None = Field(
        None,
        description="ID of the detailed processing report, if enhanced reporting was used.",
    )


class FileUploadProcessArgs(BaseModel):
    """Arguments for file upload processing."""

    pass


# Add missing schemas that router expects
class FileSchemaRequest(BaseModel):
    """Request for file schema analysis."""

    upload_id: str
    file_path: str


class FileSchemaResponse(BaseModel):
    """Response for file schema analysis."""

    headers: list[str]
    sample_data: list[dict] | None = None


class FileUploadResponse(BaseModel):
    """Response for file upload."""

    upload_id: str
    filename: str
    status: str = "uploaded"


class ColumnMappingRequest(BaseModel):
    """Request for column mapping."""

    upload_id: str
    mappings: dict[str, str]


class ProcessFileRequest(BaseModel):
    """Request to process a file."""

    upload_id: str
    column_mappings: dict[str, str] | None = None


class InterpretationResultSchema(BaseModel):
    """Result of file interpretation."""

    upload_id: str
    status: str
    headers: list[str]
    column_mappings: dict[str, str] | None = None
    sample_data: list[dict] | None = None
    message: str | None = None
    tenant_id: int | None = None
    user_id: str | None = None


class ConfirmInterpretationRequest(BaseModel):
    """Request to confirm interpretation results."""

    upload_id: str
    confirmed_mappings: dict[str, str]


class ConfirmInterpretationResponse(BaseModel):
    """Response after confirming interpretation."""

    upload_id: str
    status: str
    transactions_created: int
    message: str


class SheetInfo(BaseModel):
    """Information about a sheet in a file."""

    name: str = Field(..., description="Sheet name")
    columns: list[str] = Field(..., description="Column headers")
    row_count: int = Field(..., description="Number of rows")
    sample_data: list[dict[str, Any]] = Field(
        default_factory=list, description="Sample data rows"
    )


class FileProcessingResult(BaseModel):
    """Result of file processing operation."""

    success: bool = Field(..., description="Whether processing was successful")
    filename: str = Field(..., description="Original filename")
    upload_id: str = Field(..., description="Unique upload identifier")
    sheets: list[SheetInfo] = Field(
        default_factory=list, description="Processed sheet information"
    )
    total_rows: int = Field(default=0, description="Total rows across all sheets")
    processed_at: str = Field(..., description="Processing timestamp")
    error_message: str | None = Field(
        None, description="Error message if processing failed"
    )


# Example of how a more detailed Upload model might look if it were to be returned from DB directly
# For now, UploadResponse is simpler for the initial API contract.
# class UploadSchema(BaseModel):
#     id: str
#     filename: str
#     content_type: Optional[str] = None
#     size: int
#     status: str
#     error_message: Optional[str] = None
#     headers: Optional[List[str]] = None
#     column_mapping: Optional[Dict[str, Optional[str]]] = None
#     user_id: Optional[int] = None # Assuming User model and ID
#     tenant_id: Optional[int] = None # Assuming Tenant model and ID
#     created_at: datetime
#     updated_at: datetime

#     class Config:
#         orm_mode = True # For SQLAlchemy model conversion
#         from_attributes = True # Pydantic v2


# ===== MISSING SCHEMAS FOR INTERPRETATION STORAGE =====


class ColumnMappingSchema(BaseModel):
    """Schema for individual column mapping storage."""

    source_column: str = Field(..., description="Original column name from file")
    target_field: str = Field(..., description="Mapped target field")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    reasoning: str | None = Field(None, description="AI reasoning for mapping")
    data_type_detected: str | None = Field(None, description="Detected data type")
    is_user_modified: bool = Field(
        default=False, description="Whether user modified mapping"
    )


class CategorizationColumnSchema(BaseModel):
    """Schema for categorization column analysis."""

    column_name: str = Field(..., description="Column name")
    categorization_relevance: float = Field(
        ..., ge=0.0, le=1.0, description="Relevance score"
    )
    categorization_role: str = Field(..., description="Role in categorization")
    importance_weight: float = Field(default=1.0, description="Importance weight")
    preferred_for_embedding: bool = Field(
        default=False, description="Use for RAG embeddings"
    )


class InferredHierarchySchema(BaseModel):
    """Schema for inferred category hierarchy from file analysis."""

    detected_categories: list[str] = Field(
        default_factory=list, description="Detected category names"
    )
    category_patterns: dict[str, list[str]] = Field(
        default_factory=dict, description="Category pattern mapping"
    )
    hierarchy_confidence: float = Field(
        default=0.0, ge=0.0, le=1.0, description="Hierarchy confidence"
    )
    suggested_mappings: dict[str, str] = Field(
        default_factory=dict, description="Suggested category mappings"
    )


class InterpretationStorageResponse(BaseModel):
    """Response schema for stored interpretation results."""

    interpretation_id: str = Field(..., description="Interpretation result ID")
    upload_id: str = Field(..., description="Upload ID")
    overall_confidence: float = Field(
        ..., description="Overall interpretation confidence"
    )
    interpretation_status: str = Field(..., description="Interpretation status")
    column_mappings: list[ColumnMappingSchema] = Field(default_factory=list)
    categorization_columns: list[CategorizationColumnSchema] = Field(
        default_factory=list
    )
    is_confirmed: bool = Field(
        default=False, description="Whether interpretation is confirmed"
    )
    created_at: str = Field(..., description="Creation timestamp")


class InterpretationConfirmationRequest(BaseModel):
    """Request to confirm interpretation results."""

    interpretation_id: str = Field(..., description="Interpretation result ID")
    confirmed_mappings: dict[str, str] = Field(
        ..., description="User-confirmed mappings"
    )
    user_modifications: list[ColumnMappingSchema] | None = Field(
        None, description="User modifications"
    )


class ProcessingTemplateSchema(BaseModel):
    """Schema for file processing templates."""

    template_name: str = Field(..., description="Template name")
    file_pattern: str | None = Field(None, description="Filename pattern regex")
    expected_columns: list[str] = Field(..., description="Expected column names")
    default_mappings: dict[str, str] = Field(..., description="Default column mappings")
    success_rate: float = Field(default=0.0, description="Template success rate")
