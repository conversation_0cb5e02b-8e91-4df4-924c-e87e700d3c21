"""
Unified Files Models
===================

Consolidated file processing models using the unified base model system.
This demonstrates consolidation of file processing patterns.

Replaces patterns from:
- domains/files/models.py
- domains/files/schemas.py
- Common file processing validation patterns

Key improvements:
- Uses CRUDModel for common fields
- Uses FileProcessingModel for file metadata
- Uses StatusTrackingModel for processing status
- Uses ConfidenceValidationMixin for AI confidence scores
- Uses MetricsModel for processing metrics
"""

import uuid
from datetime import datetime
from typing import Any

from pydantic import Field

from ...shared.models import (
    ConfidenceValidationMixin,
    CreateSchema,
    CRUDModel,
    FileProcessingModel,
    ListResponseSchema,
    MetricsModel,
    ResponseSchema,
    StatusTrackingModel,
    UpdateSchema,
)


class FileUpload(CRUDModel, FileProcessingModel, StatusTrackingModel, MetricsModel):
    """
    Unified file upload model with comprehensive tracking.
    
    Consolidates common patterns:
    - CRUDModel: id, tenant_id, user_id, created_at, updated_at
    - FileProcessingModel: filename, file_size, file_type, upload_id
    - StatusTrackingModel: status, started_at, completed_at, error_message
    - MetricsModel: processing metrics and analytics
    """
    
    # File storage information
    storage_path: str | None = Field(None, max_length=1000, description="Path to stored file")
    storage_bucket: str | None = Field(None, max_length=255, description="GCS bucket name")
    file_hash: str | None = Field(None, max_length=64, description="SHA-256 hash for integrity")
    
    # File content analysis
    row_count: int = Field(default=0, ge=0, description="Number of data rows")
    column_count: int = Field(default=0, ge=0, description="Number of columns")
    detected_encoding: str | None = Field(None, max_length=50, description="File encoding detected")
    
    # Processing results
    upload_status: str = Field(default="pending", description="Upload status: pending, processing, completed, failed")
    processing_status: str = Field(default="not_started", description="Processing pipeline status")
    is_processed: bool = Field(default=False, description="Has file been fully processed")
    
    # Business results
    transactions_created: int = Field(default=0, ge=0, description="Number of transactions created")
    categorization_accuracy: float | None = Field(None, ge=0.0, le=1.0, description="AI categorization accuracy")
    
    # Additional timestamps
    uploaded_at: datetime | None = Field(None, description="When upload completed")
    processed_at: datetime | None = Field(None, description="When processing completed")

    @property
    def processing_success_rate(self) -> float:
        """Calculate processing success rate."""
        if self.row_count == 0:
            return 0.0
        processed = getattr(self, 'processed_count', 0)
        return (processed / self.row_count) * 100.0 if processed else 0.0

    @property
    def is_upload_complete(self) -> bool:
        """Check if upload is complete."""
        return self.upload_status == "completed"

    @property
    def has_processing_errors(self) -> bool:
        """Check if there were processing errors."""
        return bool(self.error_message) or self.upload_status == "failed"


class InterpretationResult(CRUDModel, ConfidenceValidationMixin, StatusTrackingModel):
    """
    Unified interpretation result model for AI schema analysis.
    
    Consolidates common patterns:
    - CRUDModel: id, tenant_id, user_id, created_at, updated_at
    - ConfidenceValidationMixin: confidence score validation
    - StatusTrackingModel: status, started_at, completed_at, error_message
    """
    
    # File reference
    upload_id: str = Field(..., max_length=255, description="Upload identifier")
    filename: str = Field(..., max_length=500, description="Original filename")
    
    # AI interpretation results
    overall_confidence: float = Field(default=0.0, ge=0.0, le=1.0, description="Overall interpretation confidence")
    interpretation_status: str = Field(default="pending", description="Status: pending, completed, failed")
    
    # Schema analysis results
    detected_headers: list[str] | None = Field(None, description="List of detected column headers")
    sample_data: list[dict[str, Any]] | None = Field(None, description="Sample rows for validation")
    row_count: int = Field(default=0, ge=0, description="Number of rows analyzed")
    
    # AI metadata
    ai_model_used: str | None = Field(None, max_length=100, description="AI model used for interpretation")
    processing_time_ms: int | None = Field(None, ge=0, description="Processing time in milliseconds")
    interpretation_summary: str | None = Field(None, description="Summary of interpretation results")
    
    # Required field mapping status
    required_fields_mapped: dict[str, bool] | None = Field(
        None, description='Required field mapping status: {"date": true, "amount": false, ...}'
    )
    
    # Processing flags
    is_confirmed: bool = Field(default=False, description="User has confirmed interpretation")
    is_processed: bool = Field(default=False, description="Interpretation has been processed")
    has_errors: bool = Field(default=False, description="Interpretation has errors")
    error_details: dict[str, Any] | None = Field(None, description="Detailed error information")
    
    # Additional timestamps
    confirmed_at: datetime | None = Field(None, description="When interpretation was confirmed")
    processed_at: datetime | None = Field(None, description="When interpretation was processed")

    @property
    def is_high_confidence(self) -> bool:
        """Check if interpretation has high confidence."""
        return self.overall_confidence >= 0.8

    @property
    def requires_review(self) -> bool:
        """Check if interpretation requires manual review."""
        return self.overall_confidence < 0.7 or self.has_errors

    @property
    def completion_status(self) -> str:
        """Get human-readable completion status."""
        if self.is_processed:
            return "completed"
        elif self.is_confirmed:
            return "confirmed"
        elif self.has_errors:
            return "failed"
        else:
            return "pending"


class ColumnMapping(CRUDModel, ConfidenceValidationMixin):
    """
    Unified column mapping model for field interpretation.
    
    Consolidates common patterns:
    - CRUDModel: id, tenant_id, user_id, created_at, updated_at
    - ConfidenceValidationMixin: confidence score validation
    """
    
    # Mapping relationship
    interpretation_result_id: uuid.UUID = Field(..., description="Parent interpretation result ID")
    source_column: str = Field(..., max_length=255, description="Source column name from file")
    target_field: str = Field(..., max_length=100, description="Target field in system")
    
    # AI analysis
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="AI confidence in mapping")
    reasoning: str | None = Field(None, description="AI reasoning for mapping decision")
    
    # Data analysis
    sample_values: list[str] | None = Field(None, description="Sample values from column")
    data_type_detected: str | None = Field(None, max_length=50, description="Detected data type")
    pattern_detected: str | None = Field(None, max_length=100, description="Detected pattern")
    
    # Validation results
    is_required_field: bool = Field(default=False, description="Is this a required field")
    validation_passed: bool = Field(default=True, description="Did validation pass")
    validation_errors: list[str] | None = Field(None, description="Validation error messages")
    
    # User modifications
    is_user_modified: bool = Field(default=False, description="User has modified this mapping")
    original_target_field: str | None = Field(None, max_length=100, description="Original AI suggestion")
    user_confidence: float | None = Field(None, ge=0.0, le=1.0, description="User confidence override")
    
    # Processing metadata
    processing_order: int | None = Field(None, description="Order in processing pipeline")

    @property
    def is_high_confidence_mapping(self) -> bool:
        """Check if mapping has high confidence."""
        return self.confidence_score >= 0.8

    @property
    def effective_confidence(self) -> float:
        """Get effective confidence (user override or AI)."""
        return self.user_confidence if self.user_confidence is not None else self.confidence_score

    @property
    def mapping_source(self) -> str:
        """Get source of mapping decision."""
        if self.is_user_modified:
            return "user_modified"
        else:
            return "ai_generated"


# CRUD Schemas using unified base schemas

class FileUploadCreate(CreateSchema):
    """Schema for creating a new file upload."""
    
    filename: str = Field(..., max_length=500, description="Original filename")
    file_size: int = Field(..., ge=0, description="File size in bytes")
    file_type: str | None = Field(None, description="MIME type or file extension")
    storage_path: str | None = Field(None, max_length=1000, description="Storage path")


class FileUploadUpdate(UpdateSchema):
    """Schema for updating an existing file upload."""
    
    upload_status: str | None = Field(None, description="Upload status")
    processing_status: str | None = Field(None, description="Processing status")
    is_processed: bool | None = Field(None, description="Processing completion flag")
    transactions_created: int | None = Field(None, ge=0, description="Transactions created count")
    categorization_accuracy: float | None = Field(None, ge=0.0, le=1.0, description="Categorization accuracy")


class FileUploadResponse(ResponseSchema, FileUpload):
    """Schema for file upload API responses."""
    
    # Additional computed fields for responses
    processing_progress: float | None = Field(None, ge=0.0, le=100.0, description="Processing progress percentage")


class FileUploadListResponse(ListResponseSchema):
    """Schema for paginated file upload list responses."""
    
    uploads: list[FileUploadResponse] = Field(..., description="List of file uploads")
    
    # Summary statistics
    total_files_size: int = Field(default=0, ge=0, description="Total size of all files")
    completed_uploads: int = Field(default=0, ge=0, description="Number of completed uploads")
    failed_uploads: int = Field(default=0, ge=0, description="Number of failed uploads")
    
    @property
    def success_rate(self) -> float:
        """Calculate upload success rate percentage."""
        if self.total == 0:
            return 0.0
        return (self.completed_uploads / self.total) * 100.0


class InterpretationResultCreate(CreateSchema):
    """Schema for creating a new interpretation result."""
    
    upload_id: str = Field(..., max_length=255, description="Upload identifier")
    filename: str = Field(..., max_length=500, description="Original filename")
    overall_confidence: float = Field(default=0.0, ge=0.0, le=1.0, description="Overall confidence")


class InterpretationResultUpdate(UpdateSchema):
    """Schema for updating an existing interpretation result."""
    
    interpretation_status: str | None = Field(None, description="Interpretation status")
    is_confirmed: bool | None = Field(None, description="Confirmation status")
    is_processed: bool | None = Field(None, description="Processing status")
    has_errors: bool | None = Field(None, description="Error status")


class InterpretationResultResponse(ResponseSchema, InterpretationResult):
    """Schema for interpretation result API responses."""
    
    pass  # Inherits all fields from InterpretationResult and ResponseSchema


# Export all unified models
__all__ = [
    # Core models
    "FileUpload",
    "InterpretationResult", 
    "ColumnMapping",
    
    # CRUD schemas
    "FileUploadCreate",
    "FileUploadUpdate",
    "FileUploadResponse",
    "FileUploadListResponse",
    "InterpretationResultCreate",
    "InterpretationResultUpdate",
    "InterpretationResultResponse",
]
