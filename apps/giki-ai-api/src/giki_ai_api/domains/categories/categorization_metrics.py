"""
Real Categorization Metrics
===========================

This module provides real categorization metrics based on actual data,
replacing fake accuracy displays with meaningful categorization status.
"""

import logging
from dataclasses import dataclass

import asyncpg

logger = logging.getLogger(__name__)


@dataclass
class CategorizationMetrics:
    """Real categorization metrics based on actual data."""
    
    # Core counts
    total_transactions: int
    categorized_transactions: int
    uncategorized_transactions: int
    
    # Categorization breakdown
    ai_categorized: int
    user_categorized: int
    business_logic_categorized: int
    
    # Confidence distribution
    high_confidence_count: int  # > 80%
    medium_confidence_count: int  # 50-80%
    low_confidence_count: int  # < 50%
    
    # Category distribution (top categories)
    top_categories: list[dict[str, any]]
    
    # Processing status
    pending_review_count: int
    user_confirmed_count: int
    
    # Enhancement status
    has_historical_data: bool
    has_vendor_mappings: bool
    has_pattern_rules: bool
    
    @property
    def categorization_rate(self) -> float:
        """Calculate real categorization rate."""
        if self.total_transactions == 0:
            return 0.0
        return (self.categorized_transactions / self.total_transactions) * 100
    
    @property
    def user_engagement_rate(self) -> float:
        """Calculate user engagement rate."""
        if self.categorized_transactions == 0:
            return 0.0
        return (self.user_categorized / self.categorized_transactions) * 100
    
    @property
    def confidence_distribution(self) -> dict[str, float]:
        """Get confidence distribution percentages."""
        total_with_confidence = self.high_confidence_count + self.medium_confidence_count + self.low_confidence_count
        if total_with_confidence == 0:
            return {"high": 0.0, "medium": 0.0, "low": 0.0}
        
        return {
            "high": (self.high_confidence_count / total_with_confidence) * 100,
            "medium": (self.medium_confidence_count / total_with_confidence) * 100,
            "low": (self.low_confidence_count / total_with_confidence) * 100,
        }


class CategorizationMetricsService:
    """Service for calculating real categorization metrics."""
    
    def __init__(self, db: asyncpg.Connection):
        self.db = db
    
    async def get_categorization_metrics(self, tenant_id: int) -> CategorizationMetrics:
        """
        Calculate real categorization metrics for a tenant.
        
        Args:
            tenant_id: The tenant ID
            
        Returns:
            CategorizationMetrics with actual data
        """
        try:
            # Get basic transaction counts
            basic_counts = await self._get_basic_counts(tenant_id)
            
            # Get categorization breakdown
            categorization_breakdown = await self._get_categorization_breakdown(tenant_id)
            
            # Get confidence distribution
            confidence_distribution = await self._get_confidence_distribution(tenant_id)
            
            # Get top categories
            top_categories = await self._get_top_categories(tenant_id)
            
            # Get processing status
            processing_status = await self._get_processing_status(tenant_id)
            
            # Get enhancement status
            enhancement_status = await self._get_enhancement_status(tenant_id)
            
            return CategorizationMetrics(
                total_transactions=basic_counts["total"],
                categorized_transactions=basic_counts["categorized"],
                uncategorized_transactions=basic_counts["uncategorized"],
                ai_categorized=categorization_breakdown["ai"],
                user_categorized=categorization_breakdown["user"],
                business_logic_categorized=categorization_breakdown["business_logic"],
                high_confidence_count=confidence_distribution["high"],
                medium_confidence_count=confidence_distribution["medium"],
                low_confidence_count=confidence_distribution["low"],
                top_categories=top_categories,
                pending_review_count=processing_status["pending_review"],
                user_confirmed_count=processing_status["user_confirmed"],
                has_historical_data=enhancement_status["historical"],
                has_vendor_mappings=enhancement_status["vendor_mappings"],
                has_pattern_rules=enhancement_status["pattern_rules"],
            )
            
        except Exception as e:
            logger.error(f"Failed to calculate categorization metrics: {e}")
            # Return safe fallback metrics
            return CategorizationMetrics(
                total_transactions=0,
                categorized_transactions=0,
                uncategorized_transactions=0,
                ai_categorized=0,
                user_categorized=0,
                business_logic_categorized=0,
                high_confidence_count=0,
                medium_confidence_count=0,
                low_confidence_count=0,
                top_categories=[],
                pending_review_count=0,
                user_confirmed_count=0,
                has_historical_data=False,
                has_vendor_mappings=False,
                has_pattern_rules=False,
            )
    
    async def _get_basic_counts(self, tenant_id: int) -> dict[str, int]:
        """Get basic transaction counts."""
        sql = """
            SELECT 
                COUNT(*) as total,
                COUNT(*) FILTER (WHERE 
                    (ai_category IS NOT NULL AND ai_category != '') OR 
                    (user_confirmed_category_id IS NOT NULL) OR
                    (category_id IS NOT NULL)
                ) as categorized,
                COUNT(*) FILTER (WHERE 
                    (ai_category IS NULL OR ai_category = '') AND 
                    user_confirmed_category_id IS NULL AND
                    category_id IS NULL
                ) as uncategorized
            FROM transactions 
            WHERE tenant_id = $1
        """
        
        row = await self.db.fetchrow(sql, tenant_id)
        return {
            "total": row["total"] or 0,
            "categorized": row["categorized"] or 0,
            "uncategorized": row["uncategorized"] or 0,
        }
    
    async def _get_categorization_breakdown(self, tenant_id: int) -> dict[str, int]:
        """Get breakdown of categorization sources."""
        sql = """
            SELECT 
                COUNT(*) FILTER (WHERE 
                    (ai_category IS NOT NULL AND ai_category != '') AND
                    user_confirmed_category_id IS NULL
                ) as ai,
                COUNT(*) FILTER (WHERE 
                    user_confirmed_category_id IS NOT NULL
                ) as user,
                COUNT(*) FILTER (WHERE 
                    category_id IS NOT NULL AND
                    (ai_category IS NULL OR ai_category = '') AND
                    user_confirmed_category_id IS NULL
                ) as business_logic
            FROM transactions 
            WHERE tenant_id = $1
        """
        
        row = await self.db.fetchrow(sql, tenant_id)
        return {
            "ai": row["ai"] or 0,
            "user": row["user"] or 0,
            "business_logic": row["business_logic"] or 0,
        }
    
    async def _get_confidence_distribution(self, tenant_id: int) -> dict[str, int]:
        """Get confidence level distribution."""
        sql = """
            SELECT 
                COUNT(*) FILTER (WHERE ai_confidence > 0.8) as high,
                COUNT(*) FILTER (WHERE ai_confidence > 0.5 AND ai_confidence <= 0.8) as medium,
                COUNT(*) FILTER (WHERE ai_confidence > 0.0 AND ai_confidence <= 0.5) as low
            FROM transactions 
            WHERE tenant_id = $1 AND ai_confidence IS NOT NULL
        """
        
        row = await self.db.fetchrow(sql, tenant_id)
        return {
            "high": row["high"] or 0,
            "medium": row["medium"] or 0,
            "low": row["low"] or 0,
        }
    
    async def _get_top_categories(self, tenant_id: int, limit: int = 10) -> list[dict[str, any]]:
        """Get top categories by transaction count."""
        sql = """
            SELECT 
                COALESCE(ai_category, 'Uncategorized') as category_name,
                COUNT(*) as transaction_count,
                AVG(ai_confidence) as avg_confidence,
                SUM(ABS(amount)) as total_amount
            FROM transactions 
            WHERE tenant_id = $1
            GROUP BY COALESCE(ai_category, 'Uncategorized')
            ORDER BY COUNT(*) DESC
            LIMIT $2
        """
        
        rows = await self.db.fetch(sql, tenant_id, limit)
        
        return [
            {
                "name": row["category_name"],
                "count": row["transaction_count"],
                "avg_confidence": float(row["avg_confidence"] or 0.0),
                "total_amount": float(row["total_amount"] or 0.0),
            }
            for row in rows
        ]
    
    async def _get_processing_status(self, tenant_id: int) -> dict[str, int]:
        """Get transaction processing status counts."""
        sql = """
            SELECT 
                COUNT(*) FILTER (WHERE 
                    ai_confidence IS NOT NULL AND 
                    ai_confidence < 0.8 AND
                    user_confirmed_category_id IS NULL
                ) as pending_review,
                COUNT(*) FILTER (WHERE 
                    user_confirmed_category_id IS NOT NULL
                ) as user_confirmed
            FROM transactions 
            WHERE tenant_id = $1
        """
        
        row = await self.db.fetchrow(sql, tenant_id)
        return {
            "pending_review": row["pending_review"] or 0,
            "user_confirmed": row["user_confirmed"] or 0,
        }
    
    async def _get_enhancement_status(self, tenant_id: int) -> dict[str, bool]:
        """Check what enhancements are available."""
        
        # Check for historical data (transactions older than 30 days)
        historical_sql = """
            SELECT COUNT(*) > 0 as has_data
            FROM transactions 
            WHERE tenant_id = $1 
            AND created_at < NOW() - INTERVAL '30 days'
        """
        historical_row = await self.db.fetchrow(historical_sql, tenant_id)
        
        # Check for vendor mappings
        vendor_sql = """
            SELECT COUNT(*) > 0 as has_mappings
            FROM vendor_category_mappings 
            WHERE tenant_id = $1
        """
        # Note: This table may not exist yet, so we'll handle the exception
        try:
            vendor_row = await self.db.fetchrow(vendor_sql, tenant_id)
            has_vendor_mappings = vendor_row["has_mappings"] if vendor_row else False
        except Exception:
            has_vendor_mappings = False
        
        # Check for pattern rules (categories with high frequency)
        pattern_sql = """
            SELECT COUNT(DISTINCT ai_category) > 10 as has_patterns
            FROM transactions 
            WHERE tenant_id = $1 
            AND ai_category IS NOT NULL 
            AND ai_category != ''
        """
        pattern_row = await self.db.fetchrow(pattern_sql, tenant_id)
        
        return {
            "historical": historical_row["has_data"] if historical_row else False,
            "vendor_mappings": has_vendor_mappings,
            "pattern_rules": pattern_row["has_patterns"] if pattern_row else False,
        }
    
    async def get_categorization_trends(self, tenant_id: int, days: int = 30) -> dict[str, any]:
        """Get categorization trends over time."""
        sql = """
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_transactions,
                COUNT(*) FILTER (WHERE 
                    (ai_category IS NOT NULL AND ai_category != '') OR 
                    (user_confirmed_category_id IS NOT NULL) OR
                    (category_id IS NOT NULL)
                ) as categorized_transactions,
                AVG(ai_confidence) FILTER (WHERE ai_confidence IS NOT NULL) as avg_confidence
            FROM transactions 
            WHERE tenant_id = $1 
            AND created_at >= NOW() - INTERVAL '%s days'
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """ % days
        
        rows = await self.db.fetch(sql, tenant_id)
        
        trend_data = []
        for row in rows:
            categorized = row["categorized_transactions"] or 0
            total = row["total_transactions"] or 0
            rate = (categorized / total * 100) if total > 0 else 0
            
            trend_data.append({
                "date": row["date"].isoformat() if row["date"] else None,
                "total_transactions": total,
                "categorized_transactions": categorized,
                "categorization_rate": round(rate, 1),
                "avg_confidence": round(float(row["avg_confidence"] or 0.0), 3),
            })
        
        return {
            "trends": trend_data,
            "period_days": days,
            "data_points": len(trend_data),
        }
    
    async def get_confidence_insights(self, tenant_id: int) -> dict[str, any]:
        """Get insights about confidence scoring."""
        sql = """
            SELECT 
                ai_category,
                COUNT(*) as category_count,
                AVG(ai_confidence) as avg_confidence,
                MIN(ai_confidence) as min_confidence,
                MAX(ai_confidence) as max_confidence,
                COUNT(*) FILTER (WHERE user_confirmed_category_id IS NOT NULL) as user_confirmed_count
            FROM transactions 
            WHERE tenant_id = $1 
            AND ai_category IS NOT NULL 
            AND ai_category != ''
            AND ai_confidence IS NOT NULL
            GROUP BY ai_category
            HAVING COUNT(*) >= 3  -- Only categories with at least 3 transactions
            ORDER BY AVG(ai_confidence) DESC
        """
        
        rows = await self.db.fetch(sql, tenant_id)
        
        category_insights = []
        for row in rows:
            user_confirmed = row["user_confirmed_count"] or 0
            total = row["category_count"] or 0
            validation_rate = (user_confirmed / total * 100) if total > 0 else 0
            
            category_insights.append({
                "category": row["ai_category"],
                "transaction_count": total,
                "avg_confidence": round(float(row["avg_confidence"] or 0.0), 3),
                "min_confidence": round(float(row["min_confidence"] or 0.0), 3),
                "max_confidence": round(float(row["max_confidence"] or 0.0), 3),
                "user_validation_rate": round(validation_rate, 1),
                "confidence_range": round(float(row["max_confidence"] or 0.0) - float(row["min_confidence"] or 0.0), 3),
            })
        
        return {
            "category_insights": category_insights,
            "total_categories": len(category_insights),
        }