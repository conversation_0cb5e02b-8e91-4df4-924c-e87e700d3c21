"""
MIS Hierarchy Setup Service
==========================

Service to setup and maintain the standard MIS hierarchy for tenants.
Ensures proper parent-child relationships and hierarchical integrity.
"""

import logging
from typing import Any

from asyncpg import Connection

logger = logging.getLogger(__name__)


class HierarchySetupService:
    """Service to setup and maintain MIS category hierarchy."""
    
    def __init__(self, db_connection: Connection):
        self.db = db_connection
    
    async def setup_standard_mis_hierarchy(self, tenant_id: int) -> dict[str, Any]:
        """
        Setup the standard MIS hierarchy for a tenant.
        
        Creates a proper 3-level hierarchy:
        - Level 0: Income/Expenses (root categories)
        - Level 1: Major categories (e.g., Sales & Services, Employee Costs)
        - Level 2: Subcategories (e.g., Product Sales, Salaries & Wages)
        """
        results = {
            "created_categories": [],
            "existing_categories": [],
            "errors": []
        }
        
        # Standard MIS hierarchy structure
        hierarchy = self._get_standard_hierarchy()
        
        try:
            # Start transaction for consistency
            async with self.db.transaction():
                # Process each root category
                for root_name, major_categories in hierarchy.items():
                    # Create or get root category (Level 0)
                    root_category = await self._ensure_category(
                        tenant_id=tenant_id,
                        name=root_name,
                        parent_id=None,
                        level=0,
                        path=root_name,
                        gl_code="4000" if root_name == "Income" else "5000"
                    )
                    
                    if root_category.get("created"):
                        results["created_categories"].append(root_category)
                    else:
                        results["existing_categories"].append(root_category)
                    
                    # Process major categories (Level 1)
                    for major_name, subcategories in major_categories.items():
                        major_path = f"{root_name} > {major_name}"
                        major_category = await self._ensure_category(
                            tenant_id=tenant_id,
                            name=major_name,
                            parent_id=root_category["id"],
                            level=1,
                            path=major_path,
                            gl_code=self._generate_gl_code(root_name, major_name, None)
                        )
                        
                        if major_category.get("created"):
                            results["created_categories"].append(major_category)
                        else:
                            results["existing_categories"].append(major_category)
                        
                        # Process subcategories (Level 2)
                        for subcat_name in subcategories:
                            subcat_path = f"{major_path} > {subcat_name}"
                            subcategory = await self._ensure_category(
                                tenant_id=tenant_id,
                                name=subcat_name,
                                parent_id=major_category["id"],
                                level=2,
                                path=subcat_path,
                                gl_code=self._generate_gl_code(root_name, major_name, subcat_name)
                            )
                            
                            if subcategory.get("created"):
                                results["created_categories"].append(subcategory)
                            else:
                                results["existing_categories"].append(subcategory)
        
        except Exception as e:
            logger.error(f"Error setting up MIS hierarchy: {e}")
            results["errors"].append(str(e))
        
        return results
    
    async def _ensure_category(
        self,
        tenant_id: int,
        name: str,
        parent_id: int | None,
        level: int,
        path: str,
        gl_code: str
    ) -> dict[str, Any]:
        """Ensure a category exists, creating if necessary."""
        # Check if category already exists
        check_sql = """
            SELECT id, name, parent_id, level, path, gl_code
            FROM categories
            WHERE tenant_id = $1 AND name = $2 
              AND (parent_id = $3 OR (parent_id IS NULL AND $3 IS NULL))
        """
        
        existing = await self.db.fetchrow(check_sql, tenant_id, name, parent_id)
        
        if existing:
            return {
                "id": existing["id"],
                "name": existing["name"],
                "path": existing["path"],
                "level": existing["level"],
                "gl_code": existing["gl_code"],
                "created": False
            }
        
        # Create new category
        create_sql = """
            INSERT INTO categories (
                name, tenant_id, parent_id, level, path, gl_code,
                is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, true, NOW(), NOW())
            RETURNING id, name, path, level, gl_code
        """
        
        new_category = await self.db.fetchrow(
            create_sql, name, tenant_id, parent_id, level, path, gl_code
        )
        
        return {
            "id": new_category["id"],
            "name": new_category["name"],
            "path": new_category["path"],
            "level": new_category["level"],
            "gl_code": new_category["gl_code"],
            "created": True
        }
    
    def _get_standard_hierarchy(self) -> dict[str, dict[str, list[str]]]:
        """
        Get the professional-grade MIS hierarchy structure for Indian businesses.
        
        This structure matches or exceeds Chartered Accountant (CA) standards and ensures
        full compliance with GST, Income Tax Act, and Companies Act 2013 requirements.
        """
        return {
            "Income": {
                "Sales & Services": [
                    "Product Sales (Domestic)", 
                    "Product Sales (Export)", 
                    "Service Revenue (Domestic)", 
                    "Service Revenue (Export)",
                    "Software Services", 
                    "Consulting Income", 
                    "License Income",
                    "Maintenance & Support",
                    "Commission Income"
                ],
                "Other Operating Income": [
                    "Job Work Income",
                    "Scrap Sales", 
                    "Export Incentives",
                    "Government Grants",
                    "Insurance Claims",
                    "Duty Drawback",
                    "Other Operating Revenue"
                ],
                "Non-Operating Income": [
                    "Interest Income", 
                    "Dividend Income", 
                    "Rental Income",
                    "Investment Returns",
                    "Foreign Exchange Gain", 
                    "Miscellaneous Income"
                ]
            },
            "Expenses": {
                "Cost of Goods Sold": [
                    "Raw Materials (Indigenous)", 
                    "Raw Materials (Imported)", 
                    "Direct Labor",
                    "Manufacturing Overheads",
                    "Packing Materials", 
                    "Freight & Forwarding", 
                    "Customs Duty",
                    "Quality Control Costs"
                ],
                "Employee Benefits": [
                    "Salaries & Wages", 
                    "PF Contribution", 
                    "ESI Contribution",
                    "Gratuity Provision",
                    "Staff Welfare", 
                    "Training & Development", 
                    "Medical Benefits",
                    "Leave Encashment",
                    "Bonus & Incentives"
                ],
                "Administrative Expenses": [
                    "Office Rent", 
                    "Electricity", 
                    "Water & Utilities", 
                    "Telephone & Internet",
                    "Office Supplies", 
                    "Printing & Stationery",
                    "Security Services",
                    "Housekeeping",
                    "Insurance Premiums"
                ],
                "Professional & Legal": [
                    "Audit Fees", 
                    "Legal Fees", 
                    "Tax Consultant Fees",
                    "Company Secretary Fees",
                    "Patent & Trademark Fees", 
                    "Registration & License Fees", 
                    "Compliance Costs",
                    "Professional Consultancy"
                ],
                "Sales & Marketing": [
                    "Advertising (Print)", 
                    "Digital Marketing", 
                    "Sales Commission",
                    "Trade Promotion", 
                    "Exhibition & Events", 
                    "Sales Travel",
                    "Customer Entertainment",
                    "Market Research",
                    "Brand Promotion"
                ],
                "Finance Costs": [
                    "Interest on Term Loans", 
                    "Interest on Working Capital", 
                    "Bank Charges",
                    "LC Charges", 
                    "Processing Fees", 
                    "Foreign Exchange Loss",
                    "Late Payment Charges",
                    "Credit Card Charges"
                ],
                "Technology & Equipment": [
                    "Software Licenses", 
                    "Hardware Purchase", 
                    "Cloud Services",
                    "IT Maintenance", 
                    "Data Backup Services", 
                    "Cybersecurity",
                    "Equipment Rental",
                    "Technology Upgrades"
                ],
                "Operations & Travel": [
                    "Business Travel", 
                    "Local Conveyance", 
                    "Vehicle Maintenance",
                    "Fuel & Petrol", 
                    "Equipment Repairs", 
                    "Facility Maintenance",
                    "Courier & Logistics",
                    "Warehouse Expenses"
                ],
                "Statutory & Compliance": [
                    "GST Input Tax", 
                    "Professional Tax", 
                    "Labour License Fees",
                    "Factory License", 
                    "Environmental Clearance", 
                    "Pollution Control Fees",
                    "Trade License",
                    "Municipal Taxes"
                ]
            }
        }
    
    def _generate_gl_code(self, root: str, major: str, subcategory: str | None) -> str:
        """Generate GL codes based on hierarchy position."""
        if root == "Income":
            base = "4"
            if "Sales" in major:
                base += "1"
            else:
                base += "9"
        else:  # Expenses
            base = "5"
            category_codes = {
                "Cost of Goods Sold": "0",
                "Employee Costs": "1",
                "Office & Admin": "2",
                "Sales & Marketing": "3",
                "Finance Costs": "4",
                "Operations": "5"
            }
            base += category_codes.get(major, "9")
        
        # Add subcategory distinction
        if subcategory:
            # Use hash of subcategory name for consistent codes
            subcode = str(abs(hash(subcategory)) % 100).zfill(2)
        else:
            subcode = "00"
        
        return base + subcode
    
    async def fix_hierarchy_integrity(self, tenant_id: int) -> dict[str, Any]:
        """Fix hierarchy integrity issues for a tenant."""
        results = {
            "levels_fixed": 0,
            "paths_fixed": 0,
            "orphans_fixed": 0
        }
        
        try:
            # Fix levels
            level_fix_sql = """
                WITH RECURSIVE category_tree AS (
                    SELECT id, parent_id, 0 as calculated_level
                    FROM categories
                    WHERE parent_id IS NULL AND tenant_id = $1
                    
                    UNION ALL
                    
                    SELECT c.id, c.parent_id, ct.calculated_level + 1
                    FROM categories c
                    INNER JOIN category_tree ct ON c.parent_id = ct.id
                    WHERE c.tenant_id = $1
                )
                UPDATE categories c
                SET level = ct.calculated_level,
                    updated_at = NOW()
                FROM category_tree ct
                WHERE c.id = ct.id AND c.tenant_id = $1
                  AND c.level != ct.calculated_level
            """
            
            level_result = await self.db.execute(level_fix_sql, tenant_id)
            results["levels_fixed"] = int(level_result.split()[-1]) if level_result else 0
            
            # Fix paths
            path_fix_sql = """
                WITH RECURSIVE category_paths AS (
                    SELECT id, name, name as full_path
                    FROM categories
                    WHERE parent_id IS NULL AND tenant_id = $1
                    
                    UNION ALL
                    
                    SELECT c.id, c.name, 
                           cp.full_path || ' > ' || c.name as full_path
                    FROM categories c
                    INNER JOIN category_paths cp ON c.parent_id = cp.id
                    WHERE c.tenant_id = $1
                )
                UPDATE categories c
                SET path = cp.full_path,
                    updated_at = NOW()
                FROM category_paths cp
                WHERE c.id = cp.id AND c.tenant_id = $1
                  AND (c.path IS NULL OR c.path != cp.full_path)
            """
            
            path_result = await self.db.execute(path_fix_sql, tenant_id)
            results["paths_fixed"] = int(path_result.split()[-1]) if path_result else 0
            
        except Exception as e:
            logger.error(f"Error fixing hierarchy integrity: {e}")
            results["error"] = str(e)
        
        return results
    
    async def convert_flat_to_hierarchical(self, tenant_id: int) -> dict[str, Any]:
        """
        Convert flat hierarchical categories (with > in names) to proper parent-child structure.
        
        This fixes categories like "Travel Expenses > Airfare > International > Economy Class"
        by breaking them into proper parent-child relationships.
        """
        results = {
            "converted_categories": 0,
            "created_parents": 0,
            "errors": [],
            "details": []
        }
        
        try:
            # Find all flat hierarchical categories (level=0 with > in name)
            flat_categories_sql = """
                SELECT id, name, gl_code, description, is_active
                FROM categories
                WHERE tenant_id = $1 
                  AND level = 0 
                  AND name LIKE '%>%'
                ORDER BY LENGTH(name) - LENGTH(REPLACE(name, '>', '')) DESC, name
            """
            
            flat_categories = await self.db.fetch(flat_categories_sql, tenant_id)
            logger.info(f"Found {len(flat_categories)} flat hierarchical categories to convert")
            
            # Track created categories to avoid duplicates
            category_map = {}  # full_path -> category_id
            
            # Get existing root categories
            existing_roots_sql = """
                SELECT id, name FROM categories 
                WHERE tenant_id = $1 AND level = 0 AND parent_id IS NULL
            """
            existing_roots = await self.db.fetch(existing_roots_sql, tenant_id)
            for root in existing_roots:
                category_map[root["name"]] = root["id"]
            
            async with self.db.transaction():
                for flat_cat in flat_categories:
                    try:
                        # Split the hierarchical name
                        parts = [p.strip() for p in flat_cat["name"].split(">")]
                        
                        if len(parts) < 2:
                            continue  # Skip if not actually hierarchical
                        
                        # Build the hierarchy
                        parent_id = None
                        parent_path = ""
                        
                        for i, part in enumerate(parts):
                            # Build current path
                            if parent_path:
                                current_path = f"{parent_path} > {part}"
                            else:
                                current_path = part
                            
                            # Check if this category already exists in our map
                            if current_path in category_map:
                                parent_id = category_map[current_path]
                                parent_path = current_path
                                continue
                            
                            # Check if category exists in database
                            check_sql = """
                                SELECT id FROM categories
                                WHERE tenant_id = $1 AND name = $2 
                                  AND (parent_id = $3 OR (parent_id IS NULL AND $3 IS NULL))
                            """
                            existing_id = await self.db.fetchval(check_sql, tenant_id, part, parent_id)
                            
                            if existing_id:
                                category_map[current_path] = existing_id
                                parent_id = existing_id
                                parent_path = current_path
                                continue
                            
                            # Create new category
                            level = i
                            
                            # Generate appropriate GL code
                            if i == len(parts) - 1 and flat_cat["gl_code"]:
                                # Use the original GL code for the leaf category
                                gl_code = flat_cat["gl_code"]
                            else:
                                # Generate GL code based on position
                                gl_code = self._generate_gl_code(
                                    parts[0],  # Root category
                                    parts[1] if len(parts) > 1 else None,
                                    part if i == 2 else None
                                )
                            
                            create_sql = """
                                INSERT INTO categories (
                                    name, tenant_id, parent_id, level, path, gl_code,
                                    description, is_active, created_at, updated_at
                                )
                                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
                                RETURNING id
                            """
                            
                            new_id = await self.db.fetchval(
                                create_sql,
                                part, tenant_id, parent_id, level, current_path, gl_code,
                                flat_cat["description"] if i == len(parts) - 1 else None,
                                flat_cat["is_active"]
                            )
                            
                            category_map[current_path] = new_id
                            parent_id = new_id
                            parent_path = current_path
                            results["created_parents"] += 1
                        
                        # Update any transactions pointing to the flat category
                        # Update both ai_suggested_category and category_id references
                        update_transactions_sql = """
                            UPDATE transactions
                            SET ai_suggested_category = $1,
                                category_id = $4
                            WHERE (ai_suggested_category = $2 OR category_id = $3) 
                              AND tenant_id = $5
                        """
                        await self.db.execute(
                            update_transactions_sql, 
                            str(parent_id),  # New category ID as string
                            str(flat_cat["id"]),  # Old category ID as string
                            flat_cat["id"],  # Old category ID as integer
                            parent_id,  # New category ID as integer
                            tenant_id
                        )
                        
                        # Delete the flat category
                        delete_sql = """
                            DELETE FROM categories
                            WHERE id = $1 AND tenant_id = $2
                        """
                        await self.db.execute(delete_sql, flat_cat["id"], tenant_id)
                        
                        results["converted_categories"] += 1
                        results["details"].append({
                            "original": flat_cat["name"],
                            "parts": parts,
                            "final_id": parent_id
                        })
                        
                    except Exception as e:
                        logger.error(f"Error converting category {flat_cat['name']}: {e}")
                        results["errors"].append({
                            "category": flat_cat["name"],
                            "error": str(e)
                        })
                
        except Exception as e:
            logger.error(f"Error in convert_flat_to_hierarchical: {e}")
            results["errors"].append(str(e))
        
        return results