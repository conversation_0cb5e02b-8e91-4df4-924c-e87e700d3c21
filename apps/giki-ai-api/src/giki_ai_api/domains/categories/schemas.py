"""
Categories Domain - Unified Schemas using Base Schema System.

This module demonstrates the consolidation of category schemas using the
unified base schema system, eliminating duplicate patterns and ensuring
consistent API contracts.

Key Improvements:
- Uses base schema mixins for common patterns
- Standardized pagination and filtering
- Consistent validation patterns
- Reduced code duplication
- Type-safe schema inheritance
"""

from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, field_validator

# Temporarily remove shared schemas dependency until base_schemas is implemented
# from giki_ai_api.shared.schemas import (
#     BaseCreateRequest, BaseUpdateRequest, BaseListRequest, 
#     BaseListResponse, BaseEntityResponse, BaseImportRequest,
#     BaseImportResponse, BaseExportRequest, BaseExportResponse, BaseValidationRequest,
#     BaseValidationResponse, NameDescriptionMixin, StatusMixin, ConfidenceMixin,
#     MetadataMixin, UserTrackingFields, SchemaFactory, ValidationPresets,
# )


# ==================== DOMAIN-SPECIFIC ENUMS ====================

class GLAccountType(str, Enum):
    """GL account types for category mapping."""
    ASSET = "asset"
    LIABILITY = "liability"
    REVENUE = "revenue"
    EXPENSE = "expense"
    EQUITY = "equity"


class CategoryType(str, Enum):
    """Category types for different use cases."""
    STANDARD = "standard"
    SYSTEM = "system"
    CUSTOM = "custom"
    IMPORTED = "imported"


# ==================== BASE CATEGORY FIELDS ====================

class CategoryBaseFields(BaseModel):
    """Base fields for category entities."""
    
    name: str = Field(..., max_length=200, description="Category name")
    description: str | None = Field(None, max_length=1000, description="Category description")
    code: str | None = Field(None, max_length=50, description="Category code")
    color: str = Field("#6B7280", description="Category color (hex code)")
    parent_id: int | None = Field(None, description="Parent category ID")
    category_type: CategoryType = Field(CategoryType.STANDARD, description="Category type")
    
    # GL Code mapping fields
    gl_code: str | None = Field(None, max_length=20, description="General ledger code")
    gl_account_name: str | None = Field(None, max_length=255, description="GL account name")
    gl_account_type: GLAccountType | None = Field(None, description="GL account type")
    
    @field_validator('color')
    @classmethod
    def validate_color(cls, v):
        if not v.startswith('#') or len(v) != 7:
            raise ValueError('Color must be a valid hex code (e.g., #6B7280)')
        return v


class CategoryLearningFields(BaseModel):
    """Fields for AI learning and categorization metadata."""
    
    learned_from_onboarding: bool = Field(True, description="Whether learned during onboarding")
    frequency_score: float | None = Field(None, ge=0.0, description="Usage frequency score")
    confidence_score: float | None = Field(None, ge=0.0, le=1.0, description="AI confidence score")
    source_files: list[str] = Field(default_factory=list, description="Source files for learning")
    
    # Schema discovery fields
    original_labels: list[str] = Field(default_factory=list, description="Original category labels")
    is_unified_category: bool = Field(False, description="Whether this is a unified category")
    unified_category_id: int | None = Field(None, description="Parent unified category ID")
    schema_discovery_session_id: str | None = Field(None, description="Schema discovery session")


class CategoryHierarchyFields(BaseModel):
    """Fields for category hierarchy management."""
    
    level: int = Field(0, ge=0, description="Hierarchy level (0 = root)")
    path: str | None = Field(None, description="Full category path")
    has_children: bool = Field(False, description="Whether category has subcategories")
    child_count: int = Field(0, ge=0, description="Number of direct children")
    descendant_count: int = Field(0, ge=0, description="Total number of descendants")


# ==================== REQUEST SCHEMAS ====================

class CategoryCreateRequest(CategoryBaseFields):
    """Request schema for creating a new category."""
    
    # Override required fields for creation
    name: str = Field(..., min_length=1, max_length=255, description="Category name")


class CategoryUpdateRequest(BaseModel):
    """Request schema for updating a category."""
    
    name: str | None = Field(None, min_length=1, max_length=255, description="Category name")
    description: str | None = Field(None, max_length=1000, description="Category description")
    code: str | None = Field(None, max_length=50, description="Category code")
    color: str | None = Field(None, description="Category color (hex code)")
    parent_id: int | None = Field(None, description="Parent category ID")
    
    # GL Code fields
    gl_code: str | None = Field(None, max_length=20, description="General ledger code")
    gl_account_name: str | None = Field(None, max_length=255, description="GL account name")
    gl_account_type: GLAccountType | None = Field(None, description="GL account type")
    
    @field_validator('color')
    @classmethod
    def validate_color(cls, v):
        if v and (not v.startswith('#') or len(v) != 7):
            raise ValueError('Color must be a valid hex code (e.g., #6B7280)')
        return v


class CategoryListRequest(BaseModel):
    """Request schema for listing categories with filtering."""
    
    # Category-specific filters
    parent_id: int | None = Field(None, description="Filter by parent category")
    category_type: CategoryType | None = Field(None, description="Filter by category type")
    gl_account_type: GLAccountType | None = Field(None, description="Filter by GL account type")
    has_gl_code: bool | None = Field(None, description="Filter by GL code presence")
    level: int | None = Field(None, ge=0, description="Filter by hierarchy level")
    learned_from_onboarding: bool | None = Field(None, description="Filter by learning source")
    
    # Hierarchy options
    include_children: bool = Field(False, description="Include child categories")
    max_depth: int | None = Field(None, ge=0, description="Maximum hierarchy depth")


class CategoryBulkUpdateRequest(BaseModel):
    """Request schema for bulk category updates."""
    
    category_ids: list[int] = Field(..., min_items=1, description="Category IDs to update")
    updates: CategoryUpdateRequest = Field(..., description="Updates to apply")
    apply_to_children: bool = Field(False, description="Apply updates to child categories")


class CategoryMoveRequest(BaseModel):
    """Request schema for moving categories in hierarchy."""
    
    category_id: int = Field(..., description="Category ID to move")
    new_parent_id: int | None = Field(None, description="New parent category ID")
    position: int | None = Field(None, ge=0, description="Position within parent")


# ==================== RESPONSE SCHEMAS ====================

class CategoryResponse(CategoryBaseFields, CategoryLearningFields, CategoryHierarchyFields):
    """Response schema for category entities."""
    
    id: int = Field(..., description="Category ID")
    tenant_id: int = Field(..., description="Tenant identifier")
    created_at: datetime | None = Field(None, description="Creation timestamp")
    updated_at: datetime | None = Field(None, description="Last update timestamp")
    is_active: bool = Field(True, description="Whether category is active")


class CategoryWithConfidence(CategoryResponse):
    """Category response with AI confidence scoring."""
    
    confidence: float = Field(..., ge=0.0, le=1.0, description="AI confidence score")
    suggestion_reason: str | None = Field(None, description="Reason for AI suggestion")
    alternative_suggestions: list[str] = Field(default_factory=list, description="Alternative category suggestions")


class CategoryTree(BaseModel):
    """Hierarchical category tree structure."""
    
    category: CategoryResponse = Field(..., description="Category data")
    children: list["CategoryTree"] = Field(default_factory=list, description="Child categories")
    depth: int = Field(0, description="Current depth in tree")
    is_expanded: bool = Field(True, description="Whether node is expanded")


# Fix forward reference
CategoryTree.model_rebuild()


class CategorySuggestion(BaseModel):
    """AI-generated category suggestion."""
    
    suggested_category_id: int = Field(..., description="Suggested category ID")
    category_name: str = Field(..., description="Category name")
    category_path: str = Field(..., description="Full category path")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Suggestion confidence")
    reasoning: str = Field(..., description="AI reasoning for suggestion")
    transaction_patterns: list[str] = Field(default_factory=list, description="Matching transaction patterns")


class CategoryAnalytics(BaseModel):
    """Category usage analytics."""
    
    category_id: int = Field(..., description="Category ID")
    category_name: str = Field(..., description="Category name")
    transaction_count: int = Field(0, description="Number of transactions")
    total_amount: float = Field(0.0, description="Total transaction amount")
    average_amount: float = Field(0.0, description="Average transaction amount")
    usage_frequency: float = Field(0.0, description="Usage frequency score")
    last_used: datetime | None = Field(None, description="Last usage timestamp")
    trend_direction: str = Field("stable", description="Usage trend (increasing/decreasing/stable)")


# ==================== SPECIALIZED REQUEST/RESPONSE SCHEMAS ====================

class CategoryImportRequest(BaseModel):
    """Request schema for importing categories."""
    
    categories: list[CategoryCreateRequest] = Field(..., min_items=1, description="Categories to import")
    preserve_hierarchy: bool = Field(True, description="Preserve category hierarchy")
    generate_gl_codes: bool = Field(False, description="Auto-generate GL codes")
    gl_code_prefix: str | None = Field(None, description="GL code prefix for generation")


class CategoryExportRequest(BaseModel):
    """Request schema for exporting categories."""
    
    include_hierarchy: bool = Field(True, description="Include hierarchy information")
    include_gl_codes: bool = Field(True, description="Include GL code mappings")
    include_analytics: bool = Field(False, description="Include usage analytics")
    hierarchy_format: str = Field("nested", description="Hierarchy format (nested/flat)")


class CategoryValidationRequest(BaseModel):
    """Request schema for category validation."""
    
    categories: list[CategoryCreateRequest] = Field(..., description="Categories to validate")
    check_hierarchy: bool = Field(True, description="Validate hierarchy consistency")
    check_gl_codes: bool = Field(True, description="Validate GL code mappings")
    check_duplicates: bool = Field(True, description="Check for duplicate names/codes")


# ==================== LIST RESPONSE SCHEMAS ====================

class CategoryListResponse(BaseModel):
    """List response for categories."""
    items: list[CategoryResponse] = Field(default_factory=list)
    total: int = Field(0)

class CategoryTreeListResponse(BaseModel):
    """List response for category trees."""
    items: list[CategoryTree] = Field(default_factory=list)
    total: int = Field(0)

class CategorySuggestionListResponse(BaseModel):
    """List response for category suggestions."""
    items: list[CategorySuggestion] = Field(default_factory=list)
    total: int = Field(0)

class CategoryAnalyticsListResponse(BaseModel):
    """List response for category analytics."""
    items: list[CategoryAnalytics] = Field(default_factory=list)
    total: int = Field(0)


# ==================== GL CODE MAPPING SCHEMAS ====================

class GLCodeMappingBase(BaseModel):
    """Base fields for GL code mappings."""
    
    category_id: int = Field(..., description="Category ID")
    gl_code: str = Field(..., max_length=20, description="General ledger code")
    gl_account_name: str = Field(..., max_length=255, description="GL account name")
    gl_account_type: GLAccountType = Field(..., description="GL account type")
    is_active: bool = Field(True, description="Whether mapping is active")


class GLCodeMappingCreate(GLCodeMappingBase):
    """Request schema for creating GL code mappings."""
    pass


class GLCodeMappingResponse(GLCodeMappingBase):
    """Response schema for GL code mappings."""
    
    id: int = Field(..., description="Mapping ID")
    tenant_id: int = Field(..., description="Tenant identifier")
    created_at: datetime | None = Field(None, description="Creation timestamp")
    updated_at: datetime | None = Field(None, description="Last update timestamp")


class GLCodeBulkMapping(BaseModel):
    """Bulk GL code mapping request."""
    
    mappings: list[GLCodeMappingCreate] = Field(..., min_items=1, description="GL code mappings")
    overwrite_existing: bool = Field(False, description="Overwrite existing mappings")
    validate_codes: bool = Field(True, description="Validate GL codes against chart of accounts")


class GLCodeMappingListResponse(BaseModel):
    """List response for GL code mappings."""
    items: list[GLCodeMappingResponse] = Field(default_factory=list)
    total: int = Field(0)


# ==================== MISSING CLASSES (STUBS) ====================

class CategoryVisualization(BaseModel):
    """Stub for CategoryVisualization."""
    pass

class EnhancementAnalysisResponse(BaseModel):
    """Stub for EnhancementAnalysisResponse."""
    pass

class EnhancementOpportunity(BaseModel):
    """Stub for EnhancementOpportunity."""
    pass

class HierarchicalResults(BaseModel):
    """Stub for HierarchicalResults."""
    pass

class CategoryExportResponse(BaseModel):
    """Response for category export operations."""
    pass

class CategoryImportResponse(BaseModel):
    """Response for category import operations."""
    pass

class CategoryValidationResponse(BaseModel):
    """Response for category validation operations."""
    pass

class CategoryHierarchyValidation(BaseModel):
    """Category hierarchy validation."""
    pass

class CategoryMapping(BaseModel):
    """Category mapping."""
    pass

class CategoryRecommendation(BaseModel):
    """Category recommendation."""
    pass

class CategorySearchResult(BaseModel):
    """Category search result."""
    pass

class GLCodeMapping(BaseModel):
    """GL code mapping."""
    pass


# ==================== BACKWARD COMPATIBILITY ALIASES ====================

# Aliases for router compatibility
Category = CategoryResponse
CategoryCreate = CategoryCreateRequest  
CategoryUpdate = CategoryUpdateRequest
CategoryBulkUpdate = CategoryBulkUpdateRequest
CategoryMove = CategoryMoveRequest
CategoryImport = CategoryImportRequest
CategoryExport = CategoryExportRequest
CategoryValidation = CategoryValidationRequest
