"""
Vendor Detection Service
========================

Intelligent vendor detection and mapping for improved categorization accuracy.
Identifies recurring merchants/vendors and maintains mappings for consistency.

Features:
- Detect recurring transaction patterns
- Normalize vendor names
- Identify high-value vendors for lookup
- Cache vendor-to-category mappings
- Smart Google search integration (only for qualified vendors)
"""

import logging
import re
from dataclasses import dataclass

import asyncpg

logger = logging.getLogger(__name__)


@dataclass
class VendorPattern:
    """Detected vendor pattern from transactions."""
    
    pattern: str
    normalized_name: str
    transaction_count: int
    total_amount: float
    avg_amount: float
    category_variations: int
    confidence_score: float
    sample_descriptions: list[str]


@dataclass
class VendorDetectionResult:
    """Result of vendor detection analysis."""
    
    total_vendors_detected: int
    high_value_vendors: list[VendorPattern]
    vendors_needing_lookup: list[VendorPattern]
    vendors_with_mappings: list[VendorPattern]
    potential_accuracy_gain: float


class VendorDetectionService:
    """Service for intelligent vendor detection and mapping."""
    
    # Common vendor name patterns to normalize
    VENDOR_PATTERNS = [
        (r'AMAZON\.COM\*[A-Z0-9]+', 'Amazon'),
        (r'AMZN MKTP US\*[A-Z0-9]+', 'Amazon'),
        (r'UBER\s*\*\s*TRIP', 'Uber'),
        (r'UBER\s*\*\s*EATS', 'Uber Eats'),
        (r'GOOGLE\s*\*[A-Z0-9]+', 'Google'),
        (r'MSFT\s*\*[A-Z0-9]+', 'Microsoft'),
        (r'STARBUCKS\s+#\d+', 'Starbucks'),
        (r'WALGREENS\s+#\d+', 'Walgreens'),
        (r'CVS/PHARMACY\s+#\d+', 'CVS'),
        (r'TARGET\s+#\d+', 'Target'),
        (r'WALMART\s+#\d+', 'Walmart'),
    ]
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def detect_vendors(
        self,
        tenant_id: int,
        upload_id: str | None,
        conn: asyncpg.Connection,
        min_transactions: int = 3,
        min_confidence_for_lookup: float = 0.7
    ) -> VendorDetectionResult:
        """
        Detect vendor patterns in transactions.
        
        Args:
            tenant_id: The tenant ID
            upload_id: Optional upload ID to analyze specific upload
            conn: Database connection
            min_transactions: Minimum transactions to consider as vendor
            min_confidence_for_lookup: Minimum confidence to skip Google lookup
            
        Returns:
            VendorDetectionResult with detected vendors
        """
        self.logger.info(f"Detecting vendors for tenant {tenant_id}, upload {upload_id}")
        
        # Get transaction patterns
        query = """
            WITH transaction_patterns AS (
                SELECT 
                    description,
                    COUNT(*) as transaction_count,
                    SUM(ABS(amount)) as total_amount,
                    AVG(ABS(amount)) as avg_amount,
                    COUNT(DISTINCT category_id) as category_variations,
                    AVG(COALESCE(ai_confidence_score, 0)) as avg_confidence,
                    ARRAY_AGG(DISTINCT description ORDER BY description) as sample_descriptions
                FROM transactions
                WHERE tenant_id = $1
                %s
                GROUP BY LOWER(REGEXP_REPLACE(description, '[0-9#]+', '', 'g'))
                HAVING COUNT(*) >= $2
            )
            SELECT * FROM transaction_patterns
            ORDER BY transaction_count DESC, total_amount DESC
        """
        
        upload_filter = "AND upload_id = $3" if upload_id else ""
        params = [tenant_id, min_transactions]
        if upload_id:
            params.append(upload_id)
        
        results = await conn.fetch(query % upload_filter, *params)
        
        # Process vendor patterns
        vendor_patterns = []
        for row in results:
            pattern = self._extract_vendor_pattern(row['description'])
            normalized = self._normalize_vendor_name(row['description'])
            
            vendor = VendorPattern(
                pattern=pattern,
                normalized_name=normalized,
                transaction_count=row['transaction_count'],
                total_amount=float(row['total_amount']),
                avg_amount=float(row['avg_amount']),
                category_variations=row['category_variations'],
                confidence_score=float(row['avg_confidence']),
                sample_descriptions=row['sample_descriptions'][:3]
            )
            vendor_patterns.append(vendor)
        
        # Check existing mappings
        vendors_with_mappings = []
        vendors_needing_lookup = []
        high_value_vendors = []
        
        for vendor in vendor_patterns:
            # Check if vendor already has mapping
            mapping_exists = await self._check_vendor_mapping(
                vendor.normalized_name, tenant_id, conn
            )
            
            if mapping_exists:
                vendors_with_mappings.append(vendor)
            else:
                # Determine if vendor needs Google lookup
                if (vendor.confidence_score < min_confidence_for_lookup and 
                    vendor.transaction_count >= 10 and
                    vendor.category_variations > 1):
                    vendors_needing_lookup.append(vendor)
                
                # High value vendors (top 20% by transaction count or amount)
                if (vendor.transaction_count >= 20 or 
                    vendor.total_amount >= 10000):
                    high_value_vendors.append(vendor)
        
        # Calculate potential accuracy gain
        total_transactions = sum(v.transaction_count for v in vendor_patterns)
        unmapped_transactions = sum(
            v.transaction_count for v in vendors_needing_lookup
        )
        potential_gain = min(10, (unmapped_transactions / total_transactions * 20)) if total_transactions > 0 else 0
        
        return VendorDetectionResult(
            total_vendors_detected=len(vendor_patterns),
            high_value_vendors=high_value_vendors[:10],  # Top 10
            vendors_needing_lookup=vendors_needing_lookup[:20],  # Top 20 for lookup
            vendors_with_mappings=vendors_with_mappings,
            potential_accuracy_gain=potential_gain
        )
    
    def _extract_vendor_pattern(self, description: str) -> str:
        """Extract vendor pattern from description."""
        # Remove common variable parts
        pattern = re.sub(r'\b\d{4,}\b', '*', description)  # Long numbers
        pattern = re.sub(r'#\d+', '#*', pattern)  # Store numbers
        pattern = re.sub(r'\s+', ' ', pattern).strip()  # Normalize spaces
        return pattern.upper()
    
    def _normalize_vendor_name(self, description: str) -> str:
        """Normalize vendor name using known patterns."""
        upper_desc = description.upper()
        
        # Check against known patterns
        for pattern, normalized in self.VENDOR_PATTERNS:
            if re.search(pattern, upper_desc):
                return normalized
        
        # Basic normalization
        # Remove common suffixes
        normalized = re.sub(r'(#\d+|\*[A-Z0-9]+|\.COM).*$', '', upper_desc)
        # Remove special characters
        normalized = re.sub(r'[^\w\s]', ' ', normalized)
        # Clean up spaces
        normalized = ' '.join(normalized.split())
        
        return normalized.title()
    
    async def _check_vendor_mapping(
        self,
        vendor_name: str,
        tenant_id: int,
        conn: asyncpg.Connection
    ) -> bool:
        """Check if vendor mapping exists."""
        query = """
            SELECT EXISTS(
                SELECT 1 FROM vendor_category_mappings
                WHERE LOWER(vendor_name) = LOWER($1)
                AND tenant_id = $2
            )
        """
        result = await conn.fetchval(query, vendor_name, tenant_id)
        return result
    
    async def create_vendor_mapping(
        self,
        vendor_name: str,
        category_id: int,
        tenant_id: int,
        business_type: str | None,
        confidence_score: float,
        conn: asyncpg.Connection
    ) -> bool:
        """Create or update vendor to category mapping."""
        try:
            query = """
                INSERT INTO vendor_category_mappings (
                    vendor_name, 
                    normalized_name,
                    category_id, 
                    tenant_id, 
                    business_type,
                    confidence_threshold,
                    created_at,
                    updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (vendor_name, tenant_id) 
                DO UPDATE SET 
                    category_id = EXCLUDED.category_id,
                    business_type = EXCLUDED.business_type,
                    confidence_threshold = EXCLUDED.confidence_threshold,
                    updated_at = CURRENT_TIMESTAMP
            """
            
            normalized = self._normalize_vendor_name(vendor_name)
            await conn.execute(
                query,
                vendor_name.lower(),
                normalized,
                category_id,
                tenant_id,
                business_type,
                confidence_score
            )
            
            self.logger.info(
                f"Created vendor mapping: {vendor_name} -> category {category_id}"
            )
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create vendor mapping: {e}")
            return False
    
    async def get_vendors_for_google_lookup(
        self,
        tenant_id: int,
        conn: asyncpg.Connection,
        limit: int = 10
    ) -> list[dict[str, any]]:
        """
        Get high-value vendors that should be looked up via Google.
        
        Returns vendors that:
        - Have high transaction volume
        - Low categorization confidence
        - No existing mapping
        - Haven't been looked up recently
        """
        query = """
            WITH vendor_stats AS (
                SELECT 
                    description,
                    COUNT(*) as transaction_count,
                    SUM(ABS(amount)) as total_amount,
                    AVG(COALESCE(ai_confidence_score, 0)) as avg_confidence,
                    COUNT(DISTINCT category_id) as category_variations,
                    MAX(date) as last_transaction
                FROM transactions
                WHERE tenant_id = $1
                GROUP BY description
                HAVING COUNT(*) >= 10
                AND AVG(COALESCE(ai_confidence_score, 0)) < 0.75
            ),
            normalized_vendors AS (
                SELECT 
                    *,
                    LOWER(REGEXP_REPLACE(description, '[0-9#]+', '', 'g')) as normalized_desc
                FROM vendor_stats
            )
            SELECT DISTINCT ON (normalized_desc)
                description,
                transaction_count,
                total_amount,
                avg_confidence,
                category_variations
            FROM normalized_vendors nv
            WHERE NOT EXISTS (
                SELECT 1 FROM vendor_category_mappings vcm
                WHERE LOWER(vcm.vendor_name) = nv.normalized_desc
                AND vcm.tenant_id = $1
            )
            ORDER BY normalized_desc, transaction_count DESC
            LIMIT $2
        """
        
        results = await conn.fetch(query, tenant_id, limit)
        
        return [
            {
                "vendor_name": self._normalize_vendor_name(row['description']),
                "sample_description": row['description'],
                "transaction_count": row['transaction_count'],
                "total_amount": float(row['total_amount']),
                "avg_confidence": float(row['avg_confidence']),
                "priority_score": row['transaction_count'] * (1 - row['avg_confidence'])
            }
            for row in results
        ]
    
    async def apply_vendor_mappings(
        self,
        tenant_id: int,
        upload_id: str | None,
        conn: asyncpg.Connection
    ) -> tuple[int, float]:
        """
        Apply existing vendor mappings to transactions.
        
        Returns:
            Tuple of (transactions_updated, accuracy_improvement)
        """
        query = """
            WITH vendor_updates AS (
                SELECT 
                    t.id,
                    vcm.category_id,
                    vcm.confidence_threshold
                FROM transactions t
                JOIN vendor_category_mappings vcm ON 
                    LOWER(t.description) LIKE '%' || LOWER(vcm.vendor_name) || '%'
                WHERE t.tenant_id = $1
                AND vcm.tenant_id = $1
                AND (t.category_id IS NULL OR t.ai_confidence_score < vcm.confidence_threshold)
                %s
            )
            UPDATE transactions t
            SET 
                category_id = vu.category_id,
                ai_confidence_score = GREATEST(COALESCE(t.ai_confidence_score, 0), vu.confidence_threshold),
                updated_at = CURRENT_TIMESTAMP
            FROM vendor_updates vu
            WHERE t.id = vu.id
        """
        
        upload_filter = "AND t.upload_id = $2" if upload_id else ""
        params = [tenant_id]
        if upload_id:
            params.append(upload_id)
        
        result = await conn.execute(query % upload_filter, *params)
        
        # Extract affected rows count
        transactions_updated = int(result.split()[-1])
        accuracy_improvement = min(8.0, transactions_updated * 0.1)
        
        self.logger.info(
            f"Applied vendor mappings: {transactions_updated} transactions updated"
        )
        
        return transactions_updated, accuracy_improvement


# Singleton instance
vendor_detection_service = VendorDetectionService()