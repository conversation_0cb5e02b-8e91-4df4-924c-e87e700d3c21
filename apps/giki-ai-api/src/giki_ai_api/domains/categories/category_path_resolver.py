"""
Category Path Resolver Service
=============================

Service to resolve category paths (e.g., "Income > Sales & Services > Consulting Income") 
to proper category IDs and ensure integration between pattern recognition and hierarchical categories.
"""

import logging
from dataclasses import dataclass

from asyncpg import Connection

logger = logging.getLogger(__name__)


@dataclass
class CategoryResolution:
    """Result of category path resolution."""
    category_id: int | None
    category_path: str
    exact_match: bool
    confidence: float
    suggested_alternatives: list[str]
    created_new: bool = False


class CategoryPathResolver:
    """
    Service to resolve category paths to IDs and maintain hierarchy integrity.
    
    This bridges the gap between pattern recognition system (which returns paths)
    and the hierarchical category system (which uses IDs).
    """
    
    def __init__(self, db_connection: Connection):
        self.db = db_connection
        self._category_cache = {}  # Cache for frequently accessed categories
    
    async def resolve_path_to_id(
        self, 
        category_path: str, 
        tenant_id: int,
        create_if_missing: bool = True
    ) -> CategoryResolution:
        """
        Resolve a category path to a category ID.
        
        Args:
            category_path: Full category path (e.g., "Expenses > Insurance Expense")
            tenant_id: Tenant ID for scoping
            create_if_missing: Whether to create missing categories
            
        Returns:
            CategoryResolution with ID and metadata
        """
        # Clean and normalize the path
        normalized_path = self._normalize_path(category_path)
        
        # Check cache first
        cache_key = f"{tenant_id}:{normalized_path}"
        if cache_key in self._category_cache:
            cached = self._category_cache[cache_key]
            return CategoryResolution(
                category_id=cached["id"],
                category_path=normalized_path,
                exact_match=True,
                confidence=1.0,
                suggested_alternatives=[]
            )
        
        # Try exact match first
        exact_match = await self._find_exact_match(normalized_path, tenant_id)
        if exact_match:
            self._category_cache[cache_key] = exact_match
            return CategoryResolution(
                category_id=exact_match["id"],
                category_path=normalized_path,
                exact_match=True,
                confidence=1.0,
                suggested_alternatives=[]
            )
        
        # Try fuzzy matching
        fuzzy_matches = await self._find_fuzzy_matches(normalized_path, tenant_id)
        if fuzzy_matches:
            best_match = fuzzy_matches[0]
            alternatives = [m["path"] for m in fuzzy_matches[1:5]]  # Top 5 alternatives
            
            return CategoryResolution(
                category_id=best_match["id"],
                category_path=best_match["path"],
                exact_match=False,
                confidence=best_match["similarity"],
                suggested_alternatives=alternatives
            )
        
        # Create new category if allowed
        if create_if_missing:
            new_category = await self._create_hierarchical_category(normalized_path, tenant_id)
            if new_category:
                self._category_cache[cache_key] = new_category
                return CategoryResolution(
                    category_id=new_category["id"],
                    category_path=normalized_path,
                    exact_match=True,
                    confidence=0.8,  # Lower confidence for newly created
                    suggested_alternatives=[],
                    created_new=True
                )
        
        # Return no match
        return CategoryResolution(
            category_id=None,
            category_path=normalized_path,
            exact_match=False,
            confidence=0.0,
            suggested_alternatives=[]
        )
    
    async def resolve_multiple_paths(
        self, 
        category_paths: list[str], 
        tenant_id: int
    ) -> dict[str, CategoryResolution]:
        """Resolve multiple category paths efficiently."""
        results = {}
        
        for path in category_paths:
            results[path] = await self.resolve_path_to_id(path, tenant_id)
        
        return results
    
    async def get_category_suggestions(
        self, 
        partial_path: str, 
        tenant_id: int,
        limit: int = 10
    ) -> list[dict[str, str]]:
        """Get category suggestions for autocomplete."""
        suggestions_sql = """
            SELECT id, name, path, level
            FROM categories
            WHERE tenant_id = $1 
              AND (path ILIKE $2 OR name ILIKE $2)
              AND is_active = true
            ORDER BY level ASC, path ASC
            LIMIT $3
        """
        
        search_pattern = f"%{partial_path}%"
        rows = await self.db.fetch(suggestions_sql, tenant_id, search_pattern, limit)
        
        return [
            {
                "id": str(row["id"]),
                "name": row["name"],
                "path": row["path"],
                "level": row["level"]
            }
            for row in rows
        ]
    
    def _normalize_path(self, path: str) -> str:
        """Normalize category path for consistent matching."""
        if not path:
            return ""
        
        # Clean up the path
        normalized = path.strip()
        
        # Standardize separators
        normalized = normalized.replace(" → ", " > ")
        normalized = normalized.replace(" / ", " > ")
        normalized = normalized.replace(" | ", " > ")
        
        # Clean up spaces around separators
        parts = [part.strip() for part in normalized.split(" > ")]
        
        return " > ".join(parts)
    
    async def _find_exact_match(self, path: str, tenant_id: int) -> dict | None:
        """Find exact category match by path."""
        exact_sql = """
            SELECT id, name, path, level, gl_code
            FROM categories
            WHERE tenant_id = $1 AND path = $2 AND is_active = true
        """
        
        row = await self.db.fetchrow(exact_sql, tenant_id, path)
        return dict(row) if row else None
    
    async def _find_fuzzy_matches(self, path: str, tenant_id: int) -> list[dict]:
        """Find fuzzy category matches using similarity."""
        fuzzy_sql = """
            SELECT id, name, path, level, gl_code,
                   similarity(path, $2) as similarity
            FROM categories
            WHERE tenant_id = $1 
              AND is_active = true
              AND similarity(path, $2) > 0.3
            ORDER BY similarity DESC, level ASC
            LIMIT 10
        """
        
        rows = await self.db.fetch(fuzzy_sql, tenant_id, path)
        return [dict(row) for row in rows]
    
    async def _create_hierarchical_category(
        self, 
        path: str, 
        tenant_id: int
    ) -> dict | None:
        """Create a new hierarchical category from path."""
        parts = path.split(" > ")
        if len(parts) < 2:
            # Single level category - create under default root
            return await self._create_single_category(parts[0], None, tenant_id, 0, path)
        
        try:
            async with self.db.transaction():
                parent_id = None
                parent_path = ""
                
                for i, part in enumerate(parts):
                    # Build current path
                    if parent_path:
                        current_path = f"{parent_path} > {part}"
                    else:
                        current_path = part
                    
                    # Check if this level already exists
                    existing = await self._find_exact_match(current_path, tenant_id)
                    if existing:
                        parent_id = existing["id"]
                        parent_path = current_path
                        continue
                    
                    # Create this level
                    new_category = await self._create_single_category(
                        part, parent_id, tenant_id, i, current_path
                    )
                    
                    if new_category:
                        parent_id = new_category["id"]
                        parent_path = current_path
                    else:
                        logger.error(f"Failed to create category level: {part}")
                        return None
                
                # Return the leaf category
                return await self._find_exact_match(path, tenant_id)
        
        except Exception as e:
            logger.error(f"Error creating hierarchical category {path}: {e}")
            return None
    
    async def _create_single_category(
        self,
        name: str,
        parent_id: int | None,
        tenant_id: int,
        level: int,
        path: str
    ) -> dict | None:
        """Create a single category."""
        create_sql = """
            INSERT INTO categories (
                name, tenant_id, parent_id, level, path, 
                gl_code, is_active, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, true, NOW(), NOW())
            RETURNING id, name, path, level, gl_code
        """
        
        # Generate a basic GL code
        gl_code = self._generate_basic_gl_code(name, level)
        
        try:
            row = await self.db.fetchrow(
                create_sql, name, tenant_id, parent_id, level, path, gl_code
            )
            return dict(row) if row else None
        except Exception as e:
            logger.error(f"Error creating category {name}: {e}")
            return None
    
    def _generate_basic_gl_code(self, name: str, level: int) -> str:
        """Generate a basic GL code for new categories."""
        # Simple GL code generation
        if "income" in name.lower() or "revenue" in name.lower():
            base = "4"
        else:
            base = "5"  # Expense default
        
        # Add level-based suffix
        suffix = str(level).zfill(2)
        
        # Add name-based hash for uniqueness
        name_hash = str(abs(hash(name)) % 100).zfill(2)
        
        return f"{base}{suffix}{name_hash}"
    
    async def clear_cache(self):
        """Clear the category cache."""
        self._category_cache.clear()
    
    async def preload_categories(self, tenant_id: int):
        """Preload categories into cache for better performance."""
        preload_sql = """
            SELECT id, name, path, level, gl_code
            FROM categories
            WHERE tenant_id = $1 AND is_active = true
            ORDER BY level ASC, path ASC
        """
        
        rows = await self.db.fetch(preload_sql, tenant_id)
        
        for row in rows:
            cache_key = f"{tenant_id}:{row['path']}"
            self._category_cache[cache_key] = dict(row)
        
        logger.info(f"Preloaded {len(rows)} categories for tenant {tenant_id}")


# Global instance for easy access
category_path_resolver = None

def get_category_path_resolver(db_connection: Connection) -> CategoryPathResolver:
    """Get or create category path resolver instance."""
    global category_path_resolver
    if category_path_resolver is None:
        category_path_resolver = CategoryPathResolver(db_connection)
    return category_path_resolver