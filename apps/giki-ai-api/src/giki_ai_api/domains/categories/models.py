"""
Unified Categories Models
========================

Consolidated categories models using the unified base model system.
This demonstrates the consolidation of duplicate patterns by using shared base classes.

Replaces patterns from:
- domains/categories/models.py
- domains/categories/schemas.py  
- Common CRUD and validation patterns

Key improvements:
- Uses CRUDModel for common fields (id, tenant_id, created_at, updated_at, user_id)
- Uses ConfidenceValidationMixin for confidence scores
- Uses ConfigurableModel for GL code configuration
- Eliminates duplicate validation and serialization code
"""

from typing import Any

from pydantic import Field

from ...shared.models import (
    BaseModel,
    ConfidenceValidationMixin,
    CreateSchema,
    CRUDModel,
    ListResponseSchema,
    ResponseSchema,
    UpdateSchema,
)


class Category(CRUDModel, ConfidenceValidationMixin):
    """
    Unified category model with hierarchical structure and GL code mapping.
    
    Consolidates common patterns:
    - CRUDModel: id, tenant_id, user_id, created_at, updated_at
    - ConfidenceValidationMixin: confidence score validation and properties
    """
    
    # Core category fields
    name: str = Field(..., max_length=200, description="Category name")
    description: str | None = Field(None, description="Category description")
    color: str = Field(default="#6B7280", max_length=7, description="Hex color code")
    code: str | None = Field(None, max_length=50, description="Category code")
    
    # Hierarchical structure
    parent_id: int | None = Field(None, description="Parent category ID")
    path: str | None = Field(None, max_length=500, description="Full hierarchical path")
    level: int = Field(default=0, ge=0, description="Depth in hierarchy")
    
    # GL Code mapping for accounting integration
    gl_code: str | None = Field(None, max_length=50, description="General Ledger code")
    gl_account_name: str | None = Field(None, max_length=200, description="GL account name")
    gl_account_type: str | None = Field(
        None, max_length=50, description="GL account type (Asset, Liability, Revenue, Expense, Equity)"
    )
    
    # Learning and usage metadata
    learned_from_onboarding: bool = Field(default=True, description="Learned during onboarding")
    frequency_score: float | None = Field(
        None, ge=0, le=999.99, description="Usage frequency score"
    )
    usage_count: int = Field(default=0, ge=0, description="Number of times used")
    
    # Status and visibility
    is_active: bool = Field(default=True, description="Whether category is active")
    is_system_category: bool = Field(default=False, description="System-defined category")
    
    @property
    def is_root_category(self) -> bool:
        """Check if this is a root category (no parent)."""
        return self.parent_id is None
    
    @property
    def hierarchy_depth(self) -> int:
        """Get the hierarchy depth (alias for level)."""
        return self.level
    
    @property
    def usage_frequency_level(self) -> str:
        """Get usage frequency as a descriptive level."""
        if self.usage_count == 0:
            return "unused"
        elif self.usage_count < 10:
            return "low"
        elif self.usage_count < 50:
            return "medium"
        elif self.usage_count < 200:
            return "high"
        else:
            return "very_high"


class CategoryTree(Category):
    """
    Category with hierarchical tree structure for nested responses.
    
    Extends the base Category model with child categories for tree representations.
    """
    
    children: list['CategoryTree'] = Field(default_factory=list, description="Child categories")
    transaction_count: int | None = Field(None, ge=0, description="Number of associated transactions")
    
    @property
    def has_children(self) -> bool:
        """Check if category has child categories."""
        return len(self.children) > 0
    
    @property
    def total_descendants(self) -> int:
        """Count total number of descendant categories."""
        count = len(self.children)
        for child in self.children:
            count += child.total_descendants
        return count


class CategoryAccuracy(CRUDModel):
    """
    Model to track category accuracy results over time.
    
    Uses CRUDModel for standard fields and adds accuracy-specific metrics.
    """
    
    validation_id: str = Field(..., max_length=36, description="Validation session ID")
    month: str = Field(..., max_length=7, description="Month being validated (e.g., '2024-07')")
    accuracy: float = Field(..., ge=0, le=100.0, description="Accuracy percentage")
    total_transactions: int = Field(..., ge=0, description="Total transactions tested")
    correct_predictions: int = Field(..., ge=0, description="Number of correct predictions")
    
    @property
    def accuracy_decimal(self) -> float:
        """Get accuracy as decimal (0.0 to 1.0)."""
        return self.accuracy / 100.0
    
    @property
    def error_rate(self) -> float:
        """Calculate error rate percentage."""
        return 100.0 - self.accuracy


# CRUD Schemas using unified base schemas

class CategoryCreate(CreateSchema):
    """Schema for creating a new category."""
    
    name: str = Field(..., max_length=200, description="Category name")
    description: str | None = Field(None, description="Category description")
    color: str = Field(default="#6B7280", max_length=7, description="Hex color code")
    code: str | None = Field(None, max_length=50, description="Category code")
    parent_id: int | None = Field(None, description="Parent category ID")
    
    # GL Code mapping
    gl_code: str | None = Field(None, max_length=50, description="General Ledger code")
    gl_account_name: str | None = Field(None, max_length=200, description="GL account name")
    gl_account_type: str | None = Field(None, max_length=50, description="GL account type")
    
    # Optional metadata
    is_system_category: bool = Field(default=False, description="System-defined category")


class CategoryUpdate(UpdateSchema):
    """Schema for updating an existing category."""
    
    name: str | None = Field(None, max_length=200, description="Category name")
    description: str | None = Field(None, description="Category description")
    color: str | None = Field(None, max_length=7, description="Hex color code")
    code: str | None = Field(None, max_length=50, description="Category code")
    parent_id: int | None = Field(None, description="Parent category ID")
    
    # GL Code mapping
    gl_code: str | None = Field(None, max_length=50, description="General Ledger code")
    gl_account_name: str | None = Field(None, max_length=200, description="GL account name")
    gl_account_type: str | None = Field(None, max_length=50, description="GL account type")
    
    # Status updates
    is_active: bool | None = Field(None, description="Whether category is active")


class CategoryResponse(ResponseSchema, Category):
    """Schema for category API responses."""
    
    pass  # Inherits all fields from Category and ResponseSchema


class CategoryListResponse(ListResponseSchema):
    """Schema for paginated category list responses."""
    
    categories: list[CategoryResponse] = Field(..., description="List of categories")


class CategoryTreeResponse(ResponseSchema):
    """Schema for hierarchical category tree responses."""
    
    root_categories: list[CategoryTree] = Field(..., description="Root level categories with children")
    total_categories: int = Field(..., ge=0, description="Total number of categories")
    max_depth: int = Field(..., ge=0, description="Maximum hierarchy depth")
    category_counts: dict[str, int] = Field(
        default_factory=dict, description="Category counts by level"
    )


# Specialized schemas for category operations

class CategoryVisualization(BaseModel):
    """Schema for category visualization data."""
    
    category_id: int = Field(..., description="Category ID")
    category_name: str = Field(..., description="Category name")
    transaction_count: int = Field(..., ge=0, description="Number of transactions")
    total_amount: float = Field(..., description="Total transaction amount")
    percentage: float = Field(..., ge=0, le=100, description="Percentage of total")
    color: str = Field(..., description="Category color for visualization")


class HierarchicalResults(BaseModel):
    """Schema for hierarchical categorization results."""
    
    upload_id: str = Field(..., description="Upload ID")
    tenant_id: int = Field(..., description="Tenant ID")
    total_transactions: int = Field(..., ge=0, description="Total transactions processed")
    categorized_transactions: int = Field(..., ge=0, description="Successfully categorized transactions")
    hierarchy_levels: list[dict[str, Any]] = Field(
        default_factory=list, description="Hierarchy level breakdown"
    )
    visualizations: list[CategoryVisualization] = Field(
        default_factory=list, description="Visualization data"
    )
    
    @property
    def categorization_rate(self) -> float:
        """Calculate categorization success rate."""
        if self.total_transactions == 0:
            return 0.0
        return (self.categorized_transactions / self.total_transactions) * 100.0


# Export all unified models
__all__ = [
    # Core models
    "Category",
    "CategoryTree", 
    "CategoryAccuracy",
    
    # CRUD schemas
    "CategoryCreate",
    "CategoryUpdate",
    "CategoryResponse",
    "CategoryListResponse",
    "CategoryTreeResponse",
    
    # Specialized schemas
    "CategoryVisualization",
    "HierarchicalResults",
]
