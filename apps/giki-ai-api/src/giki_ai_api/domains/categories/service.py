"""
Categories Service - Main service interface for category operations

This module provides the main CategoryService interface that combines
CRUD operations from CategoryCrudService with additional business logic.
"""

from .crud_service import CategoryCrudService


# Export CategoryCrudService as CategoryService for backward compatibility
class CategoryService(CategoryCrudService):
    """Main category service interface extending CategoryCrudService."""
    pass


# Export for direct import
__all__ = ["CategoryService"]