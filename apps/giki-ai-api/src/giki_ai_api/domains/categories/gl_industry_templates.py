"""
Industry-Specific GL Code Templates for M3 Giki Path Enhancement
=============================================================

Provides industry-specific GL code patterns and category hierarchies to improve
AI categorization accuracy for M3 Giki customers. This addresses the strategic
priority of achieving 98%+ accuracy with complex GL code structures.
"""



class GLIndustryTemplates:
    """
    Industry-specific GL code templates and patterns for enhanced AI categorization.

    These templates help the AI understand:
    1. Industry-standard GL code ranges
    2. Common expense/revenue categories by industry
    3. Typical transaction patterns
    4. Professional accounting hierarchy standards
    """

    INDUSTRY_TEMPLATES = {
        "professional_services": {
            "name": "Professional Services",
            "description": "Legal, Consulting, Accounting, Engineering",
            "common_revenue": {
                "4000-4999": "Service Revenue",
                "4100": "Professional Fees",
                "4200": "Consulting Revenue",
                "4300": "Project Revenue",
                "4400": "Retainer Fees",
            },
            "common_expenses": {
                "5000-5999": "Cost of Services",
                "5100": "Professional Contractor Costs",
                "5200": "Subcontractor Expenses",
                "6000-6999": "Operating Expenses",
                "6100": "Salaries and Benefits",
                "6200": "Office Rent",
                "6300": "Professional Insurance",
                "6400": "Marketing and Business Development",
                "6500": "Technology and Software",
                "6600": "Professional Development",
                "7000-7999": "Administrative Expenses",
            },
            "typical_transactions": [
                "Professional software subscriptions → 6500 Technology",
                "Client entertainment → 6400 Marketing",
                "Professional liability insurance → 6300 Insurance",
                "Continuing education → 6600 Professional Development",
            ],
        },
        "technology": {
            "name": "Technology & Software",
            "description": "SaaS, Software Development, IT Services",
            "common_revenue": {
                "4000-4999": "Technology Revenue",
                "4100": "SaaS Subscription Revenue",
                "4200": "Software License Revenue",
                "4300": "Professional Services Revenue",
                "4400": "Support and Maintenance Revenue",
            },
            "common_expenses": {
                "5000-5999": "Cost of Revenue",
                "5100": "Cloud Infrastructure",
                "5200": "Third-party Software Costs",
                "5300": "Payment Processing Fees",
                "6000-6999": "Research & Development",
                "6100": "Engineering Salaries",
                "6200": "Development Tools",
                "6300": "Testing and QA",
                "7000-7999": "Sales & Marketing",
                "7100": "Digital Marketing",
                "7200": "Sales Tools and CRM",
                "8000-8999": "General & Administrative",
            },
            "typical_transactions": [
                "AWS hosting costs → 5100 Cloud Infrastructure",
                "GitHub subscription → 6200 Development Tools",
                "Google Ads spending → 7100 Digital Marketing",
                "Salesforce subscription → 7200 Sales Tools",
            ],
        },
        "manufacturing": {
            "name": "Manufacturing",
            "description": "Physical goods production and distribution",
            "common_revenue": {
                "4000-4999": "Sales Revenue",
                "4100": "Product Sales",
                "4200": "Service Revenue",
                "4300": "Shipping and Handling",
            },
            "common_expenses": {
                "5000-5999": "Cost of Goods Sold",
                "5100": "Raw Materials",
                "5200": "Direct Labor",
                "5300": "Manufacturing Overhead",
                "5400": "Freight and Shipping",
                "6000-6999": "Operating Expenses",
                "6100": "Factory Rent",
                "6200": "Equipment Maintenance",
                "6300": "Utilities",
                "6400": "Quality Control",
                "7000-7999": "Selling Expenses",
            },
            "typical_transactions": [
                "Raw material purchases → 5100 Raw Materials",
                "Factory utilities → 6300 Utilities",
                "Equipment repairs → 6200 Equipment Maintenance",
                "Shipping costs → 5400 Freight and Shipping",
            ],
        },
        "healthcare": {
            "name": "Healthcare",
            "description": "Medical practices, clinics, healthcare services",
            "common_revenue": {
                "4000-4999": "Patient Service Revenue",
                "4100": "Patient Care Revenue",
                "4200": "Insurance Reimbursements",
                "4300": "Medicare/Medicaid Revenue",
                "4400": "Other Medical Revenue",
            },
            "common_expenses": {
                "5000-5999": "Cost of Patient Care",
                "5100": "Medical Supplies",
                "5200": "Pharmaceuticals",
                "5300": "Laboratory Costs",
                "6000-6999": "Operating Expenses",
                "6100": "Medical Staff Salaries",
                "6200": "Medical Equipment Lease",
                "6300": "Medical Insurance",
                "6400": "Compliance and Regulatory",
                "7000-7999": "Administrative Expenses",
            },
            "typical_transactions": [
                "Medical supplies → 5100 Medical Supplies",
                "Equipment lease → 6200 Medical Equipment",
                "Malpractice insurance → 6300 Medical Insurance",
                "HIPAA compliance software → 6400 Compliance",
            ],
        },
        "retail": {
            "name": "Retail",
            "description": "Physical and online retail operations",
            "common_revenue": {
                "4000-4999": "Sales Revenue",
                "4100": "Product Sales",
                "4200": "Online Sales",
                "4300": "Shipping Revenue",
            },
            "common_expenses": {
                "5000-5999": "Cost of Goods Sold",
                "5100": "Inventory Purchases",
                "5200": "Freight In",
                "5300": "Purchase Discounts",
                "6000-6999": "Operating Expenses",
                "6100": "Store Rent",
                "6200": "Store Utilities",
                "6300": "Point of Sale Systems",
                "6400": "Inventory Management",
                "7000-7999": "Marketing and Advertising",
            },
            "typical_transactions": [
                "Inventory purchases → 5100 Inventory",
                "Store rent → 6100 Store Rent",
                "POS system fees → 6300 POS Systems",
                "Social media advertising → 7000 Marketing",
            ],
        },
    }

    @classmethod
    def get_industry_template(cls, industry: str) -> dict | None:
        """Get GL code template for specific industry"""
        return cls.INDUSTRY_TEMPLATES.get(industry.lower().replace(" ", "_"))

    @classmethod
    def get_gl_account_type_for_range(
        cls, gl_code: str, industry: str = None
    ) -> str | None:
        """
        Determine GL account type based on code range and industry context.

        Standard GL code ranges:
        - 1000-1999: Assets
        - 2000-2999: Liabilities
        - 3000-3999: Equity
        - 4000-4999: Revenue/Income
        - 5000-5999: Cost of Goods Sold (Expense)
        - 6000-6999: Operating Expenses
        - 7000-7999: Other Expenses
        - 8000-8999: Other Income
        - 9000-9999: Extraordinary Items
        """
        if not gl_code or not gl_code.isdigit():
            return None

        code = int(gl_code)

        if 1000 <= code <= 1999:
            return "Asset"
        elif 2000 <= code <= 2999:
            return "Liability"
        elif 3000 <= code <= 3999:
            return "Equity"
        elif 4000 <= code <= 4999:
            return "Revenue"
        elif 5000 <= code <= 7999:
            return "Expense"
        elif 8000 <= code <= 8999:
            return "Revenue"  # Other Income
        elif 9000 <= code <= 9999:
            return "Expense"  # Extraordinary Items
        else:
            return None

    @classmethod
    def get_suggested_gl_range(
        cls, category_name: str, industry: str = None
    ) -> str | None:
        """
        Suggest appropriate GL code range based on category name and industry.
        This helps the AI make more accurate GL account type suggestions.
        """
        category_lower = category_name.lower()

        # Revenue patterns
        revenue_keywords = [
            "revenue",
            "sales",
            "income",
            "fees",
            "subscription",
            "service",
        ]
        if any(keyword in category_lower for keyword in revenue_keywords):
            return "4000-4999"

        # Cost of Goods Sold patterns
        cogs_keywords = [
            "cost of goods",
            "cost of sales",
            "materials",
            "manufacturing",
            "inventory",
        ]
        if any(keyword in category_lower for keyword in cogs_keywords):
            return "5000-5999"

        # Operating Expenses patterns
        operating_keywords = [
            "rent",
            "utilities",
            "salaries",
            "insurance",
            "marketing",
            "office",
            "equipment",
        ]
        if any(keyword in category_lower for keyword in operating_keywords):
            return "6000-6999"

        # Administrative Expenses patterns
        admin_keywords = [
            "administrative",
            "legal",
            "accounting",
            "bank",
            "interest",
            "depreciation",
        ]
        if any(keyword in category_lower for keyword in admin_keywords):
            return "7000-7999"

        # Default to operating expenses for unknown categories
        return "6000-6999"

    @classmethod
    def get_industry_context_for_ai(cls, industry: str) -> str:
        """
        Generate industry-specific context for AI prompting.
        This enhances M3 Giki categorization accuracy.
        """
        template = cls.get_industry_template(industry)
        if not template:
            return ""

        context = f"""
INDUSTRY CONTEXT: {template["name"]}
Description: {template["description"]}

TYPICAL GL CODE RANGES:
Revenue: {", ".join(template["common_revenue"].keys())}
Expenses: {", ".join(template["common_expenses"].keys())}

COMMON TRANSACTION PATTERNS:
{chr(10).join(template["typical_transactions"])}

CLASSIFICATION GUIDANCE:
- Use industry-standard GL code ranges
- Consider transaction amount for classification (large amounts may be capital)
- Maintain consistency with existing GL structure
- Follow professional accounting hierarchy
"""
        return context

    @classmethod
    def get_amount_based_suggestions(
        cls, amount: float, industry: str = None
    ) -> dict[str, str]:
        """
        Provide amount-based GL account type suggestions.
        Large amounts may indicate capital expenses or significant revenue.
        """
        suggestions = {}

        if amount and abs(amount) > 10000:
            suggestions["note"] = (
                "Large amount - consider capital expense or major revenue classification"
            )

            if amount > 0:
                suggestions["likely_type"] = "Revenue"
                suggestions["consideration"] = (
                    "Major contract, large sale, or capital contribution"
                )
            else:
                suggestions["likely_type"] = "Expense"
                suggestions["consideration"] = (
                    "Capital expenditure, major equipment purchase, or significant operational cost"
                )

        elif amount and abs(amount) < 100:
            suggestions["note"] = (
                "Small amount - likely operational expense or minor revenue"
            )
            suggestions["likely_type"] = "Expense" if amount < 0 else "Revenue"
            suggestions["consideration"] = "Routine operational transaction"

        return suggestions
