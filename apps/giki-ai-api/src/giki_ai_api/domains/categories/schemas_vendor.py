"""
Vendor-specific schemas for category mapping
"""

from datetime import datetime

from pydantic import BaseModel, Field


class VendorMappingCreate(BaseModel):
    """Schema for creating a vendor-to-category mapping."""
    
    vendor_name: str = Field(description="The vendor name to map")
    category_id: int = Field(description="The category to map to")
    
    # Transaction direction awareness
    applies_to_debits: bool = Field(default=True, description="Apply to expense transactions")
    applies_to_credits: bool = Field(default=False, description="Apply to income transactions")
    
    # Optional fields for better matching
    description_pattern: str | None = Field(
        default=None, 
        description="Optional regex pattern for more specific matching"
    )
    min_amount: float | None = Field(
        default=None,
        description="Only apply to transactions >= this amount"
    )
    max_amount: float | None = Field(
        default=None,
        description="Only apply to transactions <= this amount"
    )
    
    # User notes
    notes: str | None = Field(
        default=None,
        description="User notes about this mapping"
    )


class VendorMappingUpdate(BaseModel):
    """Schema for updating a vendor mapping."""
    
    category_id: int | None = None
    applies_to_debits: bool | None = None
    applies_to_credits: bool | None = None
    description_pattern: str | None = None
    min_amount: float | None = None
    max_amount: float | None = None
    notes: str | None = None
    is_active: bool | None = None


class VendorMapping(BaseModel):
    """Complete vendor mapping information."""
    
    id: int
    vendor_name: str
    normalized_name: str
    category_id: int
    category_path: str
    
    # Direction settings
    applies_to_debits: bool
    applies_to_credits: bool
    
    # Matching rules
    description_pattern: str | None
    min_amount: float | None
    max_amount: float | None
    
    # Metadata
    confidence_threshold: float
    business_type: str | None
    notes: str | None
    is_active: bool
    
    # Usage stats
    times_applied: int
    last_applied: datetime | None
    
    # Audit
    created_at: datetime
    created_by: int
    updated_at: datetime


class VendorGrouping(BaseModel):
    """Group of transactions by vendor for bulk actions."""
    
    vendor_name: str
    normalized_name: str
    transaction_count: int
    total_amount: float
    
    # Transaction direction breakdown
    debit_count: int
    debit_total: float
    credit_count: int
    credit_total: float
    
    # Current categorization
    categories_used: list[str]
    most_common_category: str | None
    
    # Existing mapping
    has_mapping: bool
    current_mapping: VendorMapping | None
    
    # Sample transactions
    sample_transactions: list[dict]


class BulkVendorMappingRequest(BaseModel):
    """Request to create multiple vendor mappings at once."""
    
    mappings: list[VendorMappingCreate]
    apply_to_existing: bool = Field(
        default=True,
        description="Apply mappings to existing uncategorized transactions"
    )
    apply_to_future: bool = Field(
        default=True,
        description="Apply mappings to future transactions"
    )


class VendorMappingResult(BaseModel):
    """Result of applying vendor mappings."""
    
    mappings_created: int
    mappings_updated: int
    transactions_affected: int
    
    # Breakdown by direction
    debits_categorized: int
    credits_categorized: int
    
    # Conflicts
    conflicts_found: int
    conflict_details: list[dict]
    
    success: bool
    message: str