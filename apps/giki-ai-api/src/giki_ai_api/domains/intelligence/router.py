"""
Unified Intelligence Router
===========================

Consolidated intelligence router using the UnifiedBaseRouter system.
Migrates from the original intelligence/router.py with standardized patterns.

This router consolidates:
- AI agent operations
- Entity extraction services
- Accounting system detection
- Amount processing workflows
- Conversational AI interfaces
- Intelligence analytics
"""

import logging
from typing import Any

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...core.dependencies import get_current_tenant_id, get_db_session
from ...shared.routers.base_router import (
    BaseRouterConfig,
    StandardResponse,
    UnifiedBaseRouter,
)
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .schemas import (
    AccountingSystemDetectionRequest,
    AmountProcessingRequest,
    EntityExtractionRequest,
)
from .service import UnifiedIntelligenceService as IntelligenceService

logger = logging.getLogger(__name__)


class AgentParams(BaseModel):
    """Parameters for AI agent operations."""
    agent_type: str = Query("conversational", description="Type of AI agent to use")
    context_limit: int = Query(4000, description="Context limit for agent")
    temperature: float = Query(0.7, description="AI temperature setting")
    max_tokens: int = Query(1000, description="Maximum tokens for response")


class EntityParams(BaseModel):
    """Parameters for entity extraction."""
    extraction_type: str = Query("full", description="Type of entity extraction")
    confidence_threshold: float = Query(0.8, description="Confidence threshold for entities")
    include_metadata: bool = Query(True, description="Include extraction metadata")


class ProcessingParams(BaseModel):
    """Parameters for data processing operations."""
    auto_categorize: bool = Query(False, description="Auto-categorize after processing")
    validate_amounts: bool = Query(True, description="Validate amount formats")
    normalize_descriptions: bool = Query(True, description="Normalize transaction descriptions")


class UnifiedIntelligenceRouter(UnifiedBaseRouter):
    """
    Unified intelligence router providing standardized AI and intelligence functionality.
    
    Features:
    - AI agent orchestration
    - Entity extraction and processing
    - Accounting system detection
    - Amount processing workflows
    - Conversational AI interfaces
    - Intelligence analytics
    """
    
    def __init__(self):
        config = BaseRouterConfig(
            prefix="/api/v1/intelligence",
            tags=["Intelligence"],
            include_auth=True,
            include_tenant_isolation=True,
            include_caching=True,
            cache_ttl=300,  # 5 minutes for intelligence operations
            include_performance_monitoring=True,
        )
        super().__init__(config)
    
    def _register_routes(self) -> None:
        """Register all intelligence-related routes."""
        
        # AI agent operations
        self._register_agent_routes()
        
        # Entity extraction services
        self._register_entity_routes()
        
        # Processing workflows
        self._register_processing_routes()
        
        # Analytics and insights
        self._register_analytics_routes()
        
        # Utility operations
        self._register_utility_routes()
    
    def _register_agent_routes(self) -> None:
        """Register AI agent operation routes."""
        
        @self.router.post("/agents/chat", response_model=StandardResponse)
        async def chat_with_agent(
            message: str,
            agent_params: AgentParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Chat with an AI agent."""
            try:
                from .conversational_agent import ConversationalAgent
                
                agent = ConversationalAgent(conn, tenant_id)
                
                response = await agent.handle_conversation(
                    message=message,
                    user_id=user.id,
                    agent_type=agent_params.agent_type,
                    context_limit=agent_params.context_limit,
                    temperature=agent_params.temperature,
                    max_tokens=agent_params.max_tokens,
                )
                
                return StandardResponse(
                    success=True,
                    data=response,
                    message="Agent response generated successfully"
                )
                
            except Exception as e:
                logger.error(f"Error in agent chat: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to process agent chat: {e!s}"
                )
        
        @self.router.post("/agents/command", response_model=StandardResponse)
        async def process_agent_command(
            command: str,
            parameters: dict[str, Any] | None = None,
            agent_params: AgentParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Process a specific agent command."""
            try:
                from .conversational_agent import ConversationalAgent
                
                agent = ConversationalAgent(conn, tenant_id)
                
                result = await agent.process_command(
                    command=command,
                    parameters=parameters or {},
                    user_id=user.id,
                    agent_type=agent_params.agent_type,
                )
                
                return StandardResponse(
                    success=True,
                    data=result,
                    message=f"Command '{command}' executed successfully"
                )
                
            except Exception as e:
                logger.error(f"Error processing agent command {command}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to process agent command: {e!s}"
                )
        
        @self.router.get("/agents/available", response_model=StandardResponse)
        async def get_available_agents(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
        ):
            """Get list of available AI agents and their capabilities."""
            try:
                available_agents = {
                    "conversational": {
                        "name": "Conversational Agent",
                        "description": "General-purpose conversational AI",
                        "capabilities": ["chat", "question_answering", "task_assistance"],
                        "max_context": 8000,
                    },
                    "customer": {
                        "name": "Customer Agent",
                        "description": "Customer-facing support agent",
                        "capabilities": ["support", "data_queries", "explanations"],
                        "max_context": 6000,
                    },
                    "coordinator": {
                        "name": "Coordinator Agent",
                        "description": "Workflow coordination and management",
                        "capabilities": ["workflow_management", "task_coordination", "process_optimization"],
                        "max_context": 10000,
                    },
                    "analytics": {
                        "name": "Analytics Agent",
                        "description": "Data analysis and insights",
                        "capabilities": ["data_analysis", "reporting", "insights_generation"],
                        "max_context": 12000,
                    },
                }
                
                return StandardResponse(
                    success=True,
                    data=available_agents,
                    message="Available agents retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting available agents: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve available agents: {e!s}"
                )
        
        @self.router.get("/agents/status", response_model=StandardResponse)
        async def get_agent_status(
            agent_type: str | None = Query(None, description="Specific agent type to check"),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get status and health of AI agents."""
            try:
                service = IntelligenceService(conn, tenant_id)
                status_info = await service.get_agent_status(agent_type)
                
                return StandardResponse(
                    success=True,
                    data=status_info,
                    message="Agent status retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting agent status: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve agent status: {e!s}"
                )
    
    def _register_entity_routes(self) -> None:
        """Register entity extraction service routes."""
        
        @self.router.post("/entities/extract", response_model=StandardResponse)
        async def extract_entities(
            request: EntityExtractionRequest,
            entity_params: EntityParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Extract entities from text or transaction data."""
            try:
                service = IntelligenceService(conn, tenant_id)
                
                extraction_result = await service.extract_entities(
                    request=request,
                    extraction_type=entity_params.extraction_type,
                    confidence_threshold=entity_params.confidence_threshold,
                    include_metadata=entity_params.include_metadata,
                    user_id=user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data=extraction_result,
                    message="Entity extraction completed successfully"
                )
                
            except Exception as e:
                logger.error(f"Error extracting entities: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to extract entities: {e!s}"
                )
        
        @self.router.post("/entities/batch-extract", response_model=StandardResponse)
        async def batch_extract_entities(
            requests: list[EntityExtractionRequest],
            entity_params: EntityParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Extract entities from multiple texts or transactions in batch."""
            try:
                service = IntelligenceService(conn, tenant_id)
                
                batch_results = []
                for request in requests:
                    try:
                        result = await service.extract_entities(
                            request=request,
                            extraction_type=entity_params.extraction_type,
                            confidence_threshold=entity_params.confidence_threshold,
                            include_metadata=entity_params.include_metadata,
                            user_id=user.id,
                        )
                        batch_results.append(result)
                    except Exception as e:
                        logger.error(f"Error in batch entity extraction: {e}")
                        batch_results.append({
                            "success": False,
                            "error": str(e),
                            "input": request.dict() if hasattr(request, 'dict') else str(request)
                        })
                
                successful_extractions = sum(1 for r in batch_results if r.get("success", True))
                
                return StandardResponse(
                    success=True,
                    data={
                        "results": batch_results,
                        "total_requests": len(requests),
                        "successful_extractions": successful_extractions,
                        "failed_extractions": len(requests) - successful_extractions,
                    },
                    message=f"Batch entity extraction completed: {successful_extractions}/{len(requests)} successful"
                )
                
            except Exception as e:
                logger.error(f"Error in batch entity extraction: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to perform batch entity extraction: {e!s}"
                )
        
        @self.router.get("/entities/types", response_model=StandardResponse)
        async def get_entity_types(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
        ):
            """Get available entity types for extraction."""
            try:
                entity_types = {
                    "financial": {
                        "name": "Financial Entities",
                        "description": "Money amounts, currencies, financial instruments",
                        "examples": ["$1,000.00", "USD", "credit card", "bank transfer"],
                    },
                    "organizations": {
                        "name": "Organizations",
                        "description": "Company names, vendor names, institutions",
                        "examples": ["Apple Inc.", "Starbucks", "Bank of America"],
                    },
                    "dates": {
                        "name": "Dates and Times",
                        "description": "Transaction dates, deadlines, periods",
                        "examples": ["2024-01-15", "January 2024", "Q1 2024"],
                    },
                    "locations": {
                        "name": "Locations",
                        "description": "Addresses, cities, countries, regions",
                        "examples": ["New York", "123 Main St", "United States"],
                    },
                    "categories": {
                        "name": "Transaction Categories",
                        "description": "Expense categories, account types, classifications",
                        "examples": ["Office Supplies", "Travel", "Marketing"],
                    },
                    "people": {
                        "name": "People",
                        "description": "Names of individuals, contacts",
                        "examples": ["John Smith", "Jane Doe"],
                    },
                }
                
                return StandardResponse(
                    success=True,
                    data=entity_types,
                    message="Entity types retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting entity types: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve entity types: {e!s}"
                )
    
    def _register_processing_routes(self) -> None:
        """Register processing workflow routes."""
        
        @self.router.post("/processing/detect-system", response_model=StandardResponse)
        async def detect_accounting_system(
            request: AccountingSystemDetectionRequest,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Detect accounting system from file or transaction data."""
            try:
                service = IntelligenceService(conn, tenant_id)
                
                detection_result = await service.detect_accounting_system(
                    request=request,
                    user_id=user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data=detection_result,
                    message="Accounting system detection completed successfully"
                )
                
            except Exception as e:
                logger.error(f"Error detecting accounting system: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to detect accounting system: {e!s}"
                )
        
        @self.router.post("/processing/amounts", response_model=StandardResponse)
        async def process_amounts(
            request: AmountProcessingRequest,
            processing_params: ProcessingParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Process and normalize amount fields from transaction data."""
            try:
                service = IntelligenceService(conn, tenant_id)
                
                processing_result = await service.process_amount_fields(
                    request=request,
                    validate_amounts=processing_params.validate_amounts,
                    auto_categorize=processing_params.auto_categorize,
                    user_id=user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data=processing_result,
                    message="Amount processing completed successfully"
                )
                
            except Exception as e:
                logger.error(f"Error processing amounts: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to process amounts: {e!s}"
                )
        
        @self.router.post("/processing/normalize-descriptions", response_model=StandardResponse)
        async def normalize_descriptions(
            descriptions: list[str],
            processing_params: ProcessingParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Normalize transaction descriptions for better categorization."""
            try:
                service = IntelligenceService(conn, tenant_id)
                
                normalized_results = []
                for description in descriptions:
                    try:
                        normalized = await service.normalize_description(
                            description=description,
                            user_id=user.id,
                        )
                        normalized_results.append({
                            "original": description,
                            "normalized": normalized,
                            "success": True,
                        })
                    except Exception as e:
                        logger.error(f"Error normalizing description '{description}': {e}")
                        normalized_results.append({
                            "original": description,
                            "normalized": description,
                            "success": False,
                            "error": str(e),
                        })
                
                successful_normalizations = sum(1 for r in normalized_results if r.get("success"))
                
                return StandardResponse(
                    success=True,
                    data={
                        "results": normalized_results,
                        "total_descriptions": len(descriptions),
                        "successful_normalizations": successful_normalizations,
                    },
                    message=f"Description normalization completed: {successful_normalizations}/{len(descriptions)} successful"
                )
                
            except Exception as e:
                logger.error(f"Error normalizing descriptions: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to normalize descriptions: {e!s}"
                )
        
        @self.router.post("/processing/analyze-batch", response_model=StandardResponse)
        async def analyze_transaction_batch(
            transaction_ids: list[str],
            processing_params: ProcessingParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Analyze a batch of transactions for patterns and insights."""
            try:
                service = IntelligenceService(conn, tenant_id)
                
                analysis_result = await service.analyze_transaction_batch(
                    transaction_ids=transaction_ids,
                    auto_categorize=processing_params.auto_categorize,
                    normalize_descriptions=processing_params.normalize_descriptions,
                    user_id=user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data=analysis_result,
                    message=f"Transaction batch analysis completed for {len(transaction_ids)} transactions"
                )
                
            except Exception as e:
                logger.error(f"Error analyzing transaction batch: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to analyze transaction batch: {e!s}"
                )
    
    def _register_analytics_routes(self) -> None:
        """Register analytics and insights routes."""
        
        @self.router.get("/analytics/insights", response_model=StandardResponse)
        async def get_intelligence_insights(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get AI-powered insights from transaction and financial data."""
            try:
                service = IntelligenceService(conn, tenant_id)
                insights = await service.get_intelligence_insights()
                
                return StandardResponse(
                    success=True,
                    data=insights,
                    message="Intelligence insights retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting intelligence insights: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve intelligence insights: {e!s}"
                )
        
        @self.router.get("/analytics/performance", response_model=StandardResponse)
        async def get_intelligence_performance(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get performance metrics for intelligence operations."""
            try:
                service = IntelligenceService(conn, tenant_id)
                performance = await service.get_intelligence_performance()
                
                return StandardResponse(
                    success=True,
                    data=performance,
                    message="Intelligence performance metrics retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting intelligence performance: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve intelligence performance: {e!s}"
                )
    
    def _register_utility_routes(self) -> None:
        """Register utility and helper routes."""
        
        @self.router.get("/capabilities", response_model=StandardResponse)
        async def get_intelligence_capabilities(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
        ):
            """Get available intelligence capabilities and features."""
            try:
                capabilities = {
                    "ai_agents": {
                        "conversational": True,
                        "customer_support": True,
                        "workflow_coordination": True,
                        "analytics": True,
                    },
                    "entity_extraction": {
                        "financial_entities": True,
                        "organizations": True,
                        "dates_times": True,
                        "locations": True,
                        "categories": True,
                        "people": True,
                    },
                    "processing": {
                        "accounting_system_detection": True,
                        "amount_normalization": True,
                        "description_normalization": True,
                        "batch_analysis": True,
                    },
                    "analytics": {
                        "pattern_recognition": True,
                        "trend_analysis": True,
                        "anomaly_detection": True,
                        "performance_insights": True,
                    },
                    "integrations": {
                        "vertex_ai": True,
                        "openai": False,
                        "custom_models": True,
                    },
                }
                
                return StandardResponse(
                    success=True,
                    data=capabilities,
                    message="Intelligence capabilities retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting intelligence capabilities: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve intelligence capabilities: {e!s}"
                )
        
        @self.router.get("/health", response_model=StandardResponse)
        async def get_intelligence_health(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get health status of intelligence services."""
            try:
                service = IntelligenceService(conn, tenant_id)
                health_status = await service.get_health_status()
                
                return StandardResponse(
                    success=True,
                    data=health_status,
                    message="Intelligence health status retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting intelligence health: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve intelligence health: {e!s}"
                )
    
    def get_router(self) -> APIRouter:
        """Get the configured router instance."""
        return self.router


# Create module-level router instance for backward compatibility
intelligence_router = UnifiedIntelligenceRouter()
router = intelligence_router.get_router()

# Export the router class for registration
__all__ = ["UnifiedIntelligenceRouter", "router"]