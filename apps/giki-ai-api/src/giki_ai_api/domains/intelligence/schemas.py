"""
Intelligence Domain Schemas
===========================

Basic schemas for intelligence/AI operations during consolidation transition.
These are simplified schemas to enable router imports while maintaining functionality.
"""

from typing import Any

from pydantic import Field

from ...shared.models import BaseModel


class AccountingSystemDetectionRequest(BaseModel):
    """Request schema for detecting accounting system format."""
    
    columns: list[str] = Field(..., description="Column headers from the file")
    sample_data: dict[str, Any] = Field(..., description="Sample data rows")
    filename: str | None = Field(None, description="Original filename")
    cultural_context: str = Field("global", description="Cultural context for detection")


class AmountProcessingRequest(BaseModel):
    """Request schema for processing amount fields."""
    
    transaction_data: dict[str, Any] = Field(..., description="Transaction data to process")
    column_mapping: dict[str, str] = Field(..., description="Column mapping configuration")
    accounting_system: dict[str, Any] = Field(..., description="Detected accounting system info")


class EntityExtractionRequest(BaseModel):
    """Request schema for extracting entities from descriptions."""
    
    descriptions: list[str] = Field(..., description="Transaction descriptions to analyze")
    cultural_context: str = Field("global", description="Cultural context for extraction")
    extract_vendors: bool = Field(True, description="Extract vendor/merchant names")
    extract_locations: bool = Field(True, description="Extract location information")
    extract_amounts: bool = Field(True, description="Extract amount information")


class DescriptionNormalizationRequest(BaseModel):
    """Request schema for normalizing transaction descriptions."""
    
    descriptions: list[str] = Field(..., description="Descriptions to normalize")
    normalize_vendors: bool = Field(True, description="Normalize vendor names")
    extract_locations: bool = Field(True, description="Extract location information")
    standardize_format: bool = Field(True, description="Standardize formatting")
    generate_category_hints: bool = Field(False, description="Generate category hints")
    business_context: bool = Field(True, description="Apply business context")


class BatchAnalysisRequest(BaseModel):
    """Request schema for batch transaction analysis."""
    
    transactions: list[dict[str, Any]] = Field(..., description="Transactions to analyze")
    detect_patterns: bool = Field(True, description="Detect transaction patterns")
    find_anomalies: bool = Field(True, description="Find anomalous transactions")
    suggest_categories: bool = Field(True, description="Suggest categories")
    cultural_context: str = Field("global", description="Cultural context")


# Export schemas
__all__ = [
    "AccountingSystemDetectionRequest",
    "AmountProcessingRequest",
    "BatchAnalysisRequest",
    "DescriptionNormalizationRequest",
    "EntityExtractionRequest",
]