"""
Authentication domain models.

Data models for users and authentication without SQLAlchemy.
"""

from datetime import datetime

from pydantic import BaseModel, ConfigDict, EmailStr, Field


class TenantDB(BaseModel):
    """Tenant model for database records."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    name: str
    domain: str | None = None
    settings: dict | None = None
    subscription_status: str = "active"
    subscription_plan: str = "free"
    created_at: datetime
    updated_at: datetime


# Alias for backwards compatibility
Tenant = TenantDB


class UserDB(BaseModel):
    """User model for database records."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str | None = None
    email: EmailStr
    hashed_password: str
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False
    tenant_id: int
    created_at: datetime
    updated_at: datetime
    last_login: datetime | None = None
    login_count: int = 0

    # Relationship data (loaded separately)
    tenant: TenantDB | None = None

    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.is_superuser

    @property
    def full_name(self) -> str | None:
        """Get user's full name (username for now)."""
        return self.username


class User(BaseModel):
    """User model for API responses (no password)."""

    model_config = ConfigDict(from_attributes=True)

    id: int
    username: str | None = None
    email: EmailStr
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False
    tenant_id: int
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    last_login: datetime | None = None
    login_count: int = 0
    tenant: TenantDB | None = None

    @property
    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.is_superuser

    @property
    def full_name(self) -> str | None:
        """Get user's full name (username for now)."""
        return self.username


class UserCreate(BaseModel):
    """Schema for creating a new user."""

    username: str
    email: EmailStr
    password: str
    tenant_id: int | None = None
    is_superuser: bool = False


class UserUpdate(BaseModel):
    """Schema for updating a user."""

    username: str | None = None
    email: EmailStr | None = None
    password: str | None = None
    is_active: bool | None = None
    is_verified: bool | None = None
    is_superuser: bool | None = None


class Token(BaseModel):
    """OAuth2 token response."""

    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """Data encoded in JWT token."""

    username: str | None = None
    tenant_id: int | None = None
    user_id: int | None = None
