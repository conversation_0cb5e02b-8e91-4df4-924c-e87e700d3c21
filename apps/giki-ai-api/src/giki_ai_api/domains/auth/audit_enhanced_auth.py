"""
Audit-Enhanced Authentication Functions
=====================================

Enhanced authentication functions with comprehensive audit logging integration.
Provides secure authentication with full audit trail compliance.

Features:
- Authentication audit logging (success/failure)
- IP address and user agent tracking
- Failed login attempt monitoring
- Suspicious activity detection
- Security event correlation
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Tuple

from asyncpg import Connection
from fastapi import HTTPException, Request, status

from ...shared.middleware.audit_logging import (
    AuditEventType,
    AuditEventSeverity,
    audit_logger,
    log_login_failure,
    log_login_success,
)
from .models import UserDB as UserModel
from .secure_auth import (
    authenticate_user_with_db,
    create_access_token,
    create_refresh_token,
)

logger = logging.getLogger(__name__)


class AuditEnhancedAuthenticator:
    """
    Authentication service with comprehensive audit logging
    """
    
    def __init__(self):
        self.max_failed_attempts = 5
        self.lockout_duration_minutes = 15
        self.suspicious_activity_threshold = 10
    
    async def authenticate_with_audit(
        self,
        conn: Connection,
        email: str,
        password: str,
        request: Request,
        remember_me: bool = False,
        device_info: Optional[str] = None
    ) -> Tuple[Optional[UserModel], Optional[dict], Optional[str]]:
        """
        Authenticate user with comprehensive audit logging
        
        Returns:
            (user, tokens, audit_id) tuple
        """
        # Extract request context
        request_id = getattr(request.state, "request_id", "unknown")
        source_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "unknown")
        
        # Check for existing lockout
        await self._check_account_lockout(conn, email, source_ip, request_id, user_agent)
        
        # Attempt authentication
        try:
            user = await authenticate_user_with_db(conn, email, password)
            
            if user:
                # Successful authentication
                audit_id = await log_login_success(
                    email=email,
                    user_id=user.id,
                    tenant_id=user.tenant_id,
                    request_id=request_id,
                    source_ip=source_ip,
                    user_agent=user_agent
                )
                
                # Clear any failed attempts for this email
                await self._clear_failed_attempts(conn, email)
                
                # Create tokens
                tokens = await self._create_tokens_with_audit(
                    user=user,
                    remember_me=remember_me,
                    request_id=request_id,
                    source_ip=source_ip,
                    device_info=device_info
                )
                
                logger.info(f"Successful authentication for {email} from {source_ip}")
                return user, tokens, audit_id
                
            else:
                # Failed authentication
                await self._handle_failed_login(
                    conn=conn,
                    email=email,
                    source_ip=source_ip,
                    user_agent=user_agent,
                    request_id=request_id,
                    failure_reason="invalid_credentials"
                )
                
                return None, None, None
                
        except Exception as e:
            # Authentication system error
            audit_id = await audit_logger.log_event(
                event_type=AuditEventType.SYSTEM_ERROR,
                severity=AuditEventSeverity.HIGH,
                request_id=request_id,
                source_ip=source_ip,
                user_agent=user_agent,
                resource="authentication",
                action="login",
                outcome="error",
                details={
                    "email": email,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
            )
            
            logger.error(f"Authentication system error for {email}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication system temporarily unavailable"
            )
    
    async def _create_tokens_with_audit(
        self,
        user: UserModel,
        remember_me: bool,
        request_id: str,
        source_ip: str,
        device_info: Optional[str] = None
    ) -> dict:
        """Create tokens and log token creation events"""
        
        # Determine token expiry
        access_token_expires = timedelta(minutes=30)
        if remember_me:
            access_token_expires = timedelta(days=7)
        
        # Create tokens
        access_token = create_access_token(
            user=user,
            expires_delta=access_token_expires
        )
        
        refresh_token = create_refresh_token(
            user=user,
            expires_delta=timedelta(days=30)
        )
        
        # Log token creation
        await audit_logger.log_event(
            event_type=AuditEventType.AUTH_TOKEN_REFRESH,
            severity=AuditEventSeverity.LOW,
            user_id=user.id,
            tenant_id=user.tenant_id,
            request_id=request_id,
            source_ip=source_ip,
            resource="tokens",
            action="create",
            outcome="success",
            details={
                "remember_me": remember_me,
                "access_token_expires_minutes": int(access_token_expires.total_seconds() / 60),
                "device_info": device_info
            }
        )
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": int(access_token_expires.total_seconds()),
        }
    
    async def _handle_failed_login(
        self,
        conn: Connection,
        email: str,
        source_ip: str,
        user_agent: str,
        request_id: str,
        failure_reason: str
    ) -> None:
        """Handle failed login with audit logging and security monitoring"""
        
        # Log failed attempt
        await log_login_failure(
            email=email,
            failure_reason=failure_reason,
            request_id=request_id,
            source_ip=source_ip,
            user_agent=user_agent
        )
        
        # Record failed attempt in database for tracking
        await self._record_failed_attempt(conn, email, source_ip)
        
        # Check for suspicious activity
        await self._check_suspicious_activity(conn, email, source_ip, request_id)
        
        logger.warning(f"Failed login attempt for {email} from {source_ip}: {failure_reason}")
    
    async def _record_failed_attempt(
        self,
        conn: Connection,
        email: str,
        source_ip: str
    ) -> None:
        """Record failed login attempt in database"""
        
        await conn.execute(
            """
            INSERT INTO failed_login_attempts (email, source_ip, attempt_time, created_at)
            VALUES ($1, $2, NOW(), NOW())
            """,
            email, source_ip
        )
    
    async def _clear_failed_attempts(
        self,
        conn: Connection,
        email: str
    ) -> None:
        """Clear failed attempts for successful login"""
        
        await conn.execute(
            "DELETE FROM failed_login_attempts WHERE email = $1",
            email
        )
    
    async def _check_account_lockout(
        self,
        conn: Connection,
        email: str,
        source_ip: str,
        request_id: str,
        user_agent: str
    ) -> None:
        """Check if account or IP is locked due to failed attempts"""
        
        # Check failed attempts in last lockout duration
        recent_failures = await conn.fetchval(
            """
            SELECT COUNT(*) FROM failed_login_attempts
            WHERE email = $1 AND attempt_time > NOW() - INTERVAL '%s minutes'
            """,
            email, self.lockout_duration_minutes
        )
        
        if recent_failures >= self.max_failed_attempts:
            # Log account lockout event
            await audit_logger.log_event(
                event_type=AuditEventType.AUTH_ACCOUNT_LOCKED,
                severity=AuditEventSeverity.HIGH,
                request_id=request_id,
                source_ip=source_ip,
                user_agent=user_agent,
                resource="authentication",
                action="lockout_check",
                outcome="blocked",
                details={
                    "email": email,
                    "failed_attempts": recent_failures,
                    "lockout_duration_minutes": self.lockout_duration_minutes
                }
            )
            
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Account temporarily locked due to {recent_failures} failed attempts. "
                       f"Try again in {self.lockout_duration_minutes} minutes."
            )
    
    async def _check_suspicious_activity(
        self,
        conn: Connection,
        email: str,
        source_ip: str,
        request_id: str
    ) -> None:
        """Check for suspicious activity patterns"""
        
        # Check IP-based suspicious activity (many different emails)
        ip_attempts = await conn.fetchval(
            """
            SELECT COUNT(DISTINCT email) FROM failed_login_attempts
            WHERE source_ip = $1 AND attempt_time > NOW() - INTERVAL '1 hour'
            """,
            source_ip
        )
        
        if ip_attempts >= self.suspicious_activity_threshold:
            await audit_logger.log_event(
                event_type=AuditEventType.API_SUSPICIOUS_ACTIVITY,
                severity=AuditEventSeverity.CRITICAL,
                request_id=request_id,
                source_ip=source_ip,
                resource="authentication",
                action="suspicious_ip_activity",
                outcome="detected",
                details={
                    "distinct_email_attempts": ip_attempts,
                    "threshold": self.suspicious_activity_threshold,
                    "time_window": "1 hour",
                    "activity_type": "potential_brute_force"
                }
            )
            
            logger.critical(f"Suspicious activity detected from IP {source_ip}: "
                          f"{ip_attempts} different email attempts in 1 hour")
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract real client IP, accounting for proxies"""
        # Check common proxy headers in order of preference
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip.strip()
        
        return request.client.host if request.client else "unknown"


# Global instance for use in routes
audit_enhanced_authenticator = AuditEnhancedAuthenticator()


# Convenience function for route integration
async def authenticate_user_with_full_audit(
    conn: Connection,
    email: str,
    password: str,
    request: Request,
    remember_me: bool = False,
    device_info: Optional[str] = None
) -> Tuple[Optional[UserModel], Optional[dict], Optional[str]]:
    """
    Authenticate user with comprehensive audit logging
    
    Convenience function for easy integration into existing routes
    """
    return await audit_enhanced_authenticator.authenticate_with_audit(
        conn=conn,
        email=email,
        password=password,
        request=request,
        remember_me=remember_me,
        device_info=device_info
    )


__all__ = [
    "AuditEnhancedAuthenticator",
    "audit_enhanced_authenticator",
    "authenticate_user_with_full_audit"
]