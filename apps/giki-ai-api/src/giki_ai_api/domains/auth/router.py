"""
Unified Auth Router
===================

Consolidated auth router using the UnifiedBaseRouter system.
Migrates from the original auth/secure_router.py with standardized patterns.

This router consolidates:
- User authentication operations
- Token management
- Password operations
- User profile management
- Security monitoring
- Session management
"""

import logging
from datetime import datetime, timedelta

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel

from ...core.dependencies import get_db_session
from ...shared.routers.base_router import (
    BaseRouterConfig,
    StandardResponse,
    UnifiedBaseRouter,
)
from .models import User, UserDB
from .schemas import (
    OptimizedUserResponse,
    UserCreate,
    UserResponse,
    UserUpdate,
)
from .secure_auth import (
    create_access_token,
    create_refresh_token,
    get_current_active_user,
    get_password_hash,
    verify_password,
)

logger = logging.getLogger(__name__)


class AuthParams(BaseModel):
    """Parameters for authentication operations."""
    remember_me: bool = Query(False, description="Extended session duration")
    device_info: str | None = Query(None, description="Device information")
    include_permissions: bool = Query(True, description="Include user permissions")


class TokenParams(BaseModel):
    """Parameters for token operations."""
    token_type: str = Query("access", description="Type of token")
    include_refresh: bool = Query(True, description="Include refresh token")
    extended_expiry: bool = Query(False, description="Extended token expiry")


class SecurityParams(BaseModel):
    """Parameters for security operations."""
    audit_level: str = Query("standard", description="Security audit level")
    include_logs: bool = Query(False, description="Include security logs")
    time_range: int = Query(24, description="Time range in hours")


class UnifiedAuthRouter(UnifiedBaseRouter):
    """
    Unified authentication router providing standardized auth functionality.
    
    Features:
    - User authentication and authorization
    - Token management (JWT)
    - Password operations
    - User profile management
    - Security monitoring
    - Session management
    """
    
    def __init__(self):
        config = BaseRouterConfig(
            prefix="/api/v1/auth",
            tags=["Authentication"],
            enable_caching=False,  # Auth operations shouldn't be cached
            enable_pagination=True,
            enable_filtering=True,
            rate_limit=None,
        )
        super().__init__(config)
        
        # Ensure routes are set up (temporary fix during consolidation)
        if len(self.router.routes) == 0:
            self._setup_routes()
    
    def _setup_routes(self) -> None:
        """Setup all authentication-related routes."""
        
        # Core authentication
        self._register_auth_routes()
        
        # Token management
        self._register_token_routes()
        
        # User management
        self._register_user_routes()
        
        # Security operations
        self._register_security_routes()
        
        # Session management
        self._register_session_routes()
    
    def _register_auth_routes(self) -> None:
        """Register core authentication routes."""
        
        @self.router.post("/login", response_model=StandardResponse)
        async def login(
            form_data: OAuth2PasswordRequestForm = Depends(),
            auth_params: AuthParams = Depends(),
            conn: Connection = Depends(get_db_session),
        ):
            """Authenticate user and return access tokens."""
            try:
                from .secure_auth import authenticate_user_with_db as authenticate_user
                
                # Authenticate user
                user = await authenticate_user(
                    conn=conn,
                    email=form_data.username,
                    password=form_data.password,
                )
                
                if not user:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Incorrect email or password",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
                
                # Determine token expiry
                access_token_expires = timedelta(minutes=30)
                if auth_params.remember_me:
                    access_token_expires = timedelta(days=7)
                
                # Create tokens
                access_token = create_access_token(
                    user=user,
                    expires_delta=access_token_expires,
                )
                
                refresh_token = create_refresh_token(
                    user=user,
                    expires_delta=timedelta(days=30),
                )
                
                # Log successful login
                logger.info(f"User {user.email} logged in successfully")
                
                return StandardResponse(
                    success=True,
                    data={
                        "access_token": access_token,
                        "refresh_token": refresh_token,
                        "token_type": "bearer",
                        "expires_in": int(access_token_expires.total_seconds()),
                        "user": OptimizedUserResponse(
                            id=user.id,
                            email=user.email,
                            full_name=user.full_name,
                            is_admin=user.is_admin,
                            tenant_id=user.tenant_id,
                            is_active=user.is_active,
                        ).dict(),
                    },
                    message="Login successful",
                    metadata={
                        "device_info": auth_params.device_info,
                        "remember_me": auth_params.remember_me,
                        "login_time": datetime.now().isoformat(),
                    }
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error during login: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Login failed: {e!s}"
                )
        
        @self.router.post("/register", response_model=StandardResponse)
        async def register(
            user_data: UserCreate,
            tenant_id: int | None = Query(None, description="Tenant ID for registration"),
            conn: Connection = Depends(get_db_session),
        ):
            """Register a new user account."""
            try:
                from .service import AuthService
                
                auth_service = AuthService(conn)
                
                # Check if user already exists
                existing_user = await auth_service.get_user_by_email(user_data.email)
                if existing_user:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Email already registered"
                    )
                
                # Create new user
                new_user = await auth_service.create_user(
                    user_data=user_data,
                    tenant_id=tenant_id,
                )
                
                logger.info(f"New user registered: {new_user.email}")
                
                return StandardResponse(
                    success=True,
                    data={
                        "user": UserResponse(
                            id=new_user.id,
                            email=new_user.email,
                            full_name=new_user.full_name,
                            is_admin=new_user.is_admin,
                            tenant_id=new_user.tenant_id,
                            is_active=new_user.is_active,
                            created_at=new_user.created_at,
                        ).dict(),
                    },
                    message="User registered successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error during registration: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Registration failed: {e!s}"
                )
        
        @self.router.post("/logout", response_model=StandardResponse)
        async def logout(
            current_user: User = Depends(get_current_active_user),
        ):
            """Logout user and invalidate tokens."""
            try:
                # In a full implementation, this would invalidate the token
                # For now, we'll just log the logout event
                logger.info(f"User {current_user.email} logged out")
                
                return StandardResponse(
                    success=True,
                    message="Logout successful"
                )
                
            except Exception as e:
                logger.error(f"Error during logout: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Logout failed: {e!s}"
                )
    
    def _register_token_routes(self) -> None:
        """Register token management routes."""
        
        @self.router.post("/refresh", response_model=StandardResponse)
        async def refresh_token(
            refresh_token: str,
            token_params: TokenParams = Depends(),
            conn: Connection = Depends(get_db_session),
        ):
            """Refresh access token using refresh token."""
            try:
                from .secure_auth import get_user_by_id, verify_token
                
                # Verify refresh token
                payload = verify_token(refresh_token)
                user_id = payload.get("sub")
                
                if not user_id:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Invalid refresh token"
                    )
                
                # Get user
                user = await get_user_by_id(conn, int(user_id))
                if not user or not user.is_active:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="User not found or inactive"
                    )
                
                # Create new access token
                access_token_expires = timedelta(minutes=30)
                if token_params.extended_expiry:
                    access_token_expires = timedelta(hours=24)
                
                new_access_token = create_access_token(
                    user=user,
                    expires_delta=access_token_expires,
                )
                
                response_data = {
                    "access_token": new_access_token,
                    "token_type": "bearer",
                    "expires_in": int(access_token_expires.total_seconds()),
                }
                
                # Include new refresh token if requested
                if token_params.include_refresh:
                    new_refresh_token = create_refresh_token(
                        user=user,
                        expires_delta=timedelta(days=30),
                    )
                    response_data["refresh_token"] = new_refresh_token
                
                return StandardResponse(
                    success=True,
                    data=response_data,
                    message="Token refreshed successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error refreshing token: {e}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token refresh failed"
                )
        
        @self.router.post("/validate-token", response_model=StandardResponse)
        async def validate_token(
            token: str,
            conn: Connection = Depends(get_db_session),
        ):
            """Validate an access token."""
            try:
                from .secure_auth import get_user_by_id, verify_token
                
                # Verify token
                payload = verify_token(token)
                user_id = payload.get("sub")
                
                if not user_id:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Invalid token"
                    )
                
                # Get user to ensure they still exist and are active
                user = await get_user_by_id(conn, int(user_id))
                if not user or not user.is_active:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="User not found or inactive"
                    )
                
                return StandardResponse(
                    success=True,
                    data={
                        "valid": True,
                        "user_id": user.id,
                        "email": user.email,
                        "tenant_id": user.tenant_id,
                        "is_admin": user.is_admin,
                    },
                    message="Token is valid"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error validating token: {e}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token validation failed"
                )
    
    def _register_user_routes(self) -> None:
        """Register user management routes."""
        
        @self.router.get("/me", response_model=StandardResponse)
        async def get_current_user(
            auth_params: AuthParams = Depends(),
            current_user: UserDB = Depends(get_current_active_user),
        ):
            """Get current user information."""
            try:
                user_data = OptimizedUserResponse(
                    id=current_user.id,
                    email=current_user.email,
                    full_name=current_user.full_name,
                    is_admin=current_user.is_admin,
                    tenant_id=current_user.tenant_id,
                    is_active=current_user.is_active,
                ).dict()
                
                # Include permissions if requested
                if auth_params.include_permissions:
                    user_data["permissions"] = {
                        "can_admin": current_user.is_admin,
                        "can_create_users": current_user.is_admin,
                        "can_manage_tenant": current_user.is_admin,
                        "can_view_analytics": True,
                        "can_export_data": True,
                    }
                
                return StandardResponse(
                    success=True,
                    data=user_data,
                    message="Current user information retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting current user: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve user information: {e!s}"
                )
        
        @self.router.put("/me", response_model=StandardResponse)
        async def update_current_user(
            user_data: UserUpdate,
            current_user: UserDB = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Update current user information."""
            try:
                from .service import AuthService
                
                auth_service = AuthService(conn)
                
                updated_user = await auth_service.update_user(
                    user_id=current_user.id,
                    user_data=user_data,
                )
                
                return StandardResponse(
                    success=True,
                    data={
                        "user": UserResponse(
                            id=updated_user.id,
                            email=updated_user.email,
                            full_name=updated_user.full_name,
                            is_admin=updated_user.is_admin,
                            tenant_id=updated_user.tenant_id,
                            is_active=updated_user.is_active,
                            created_at=updated_user.created_at,
                        ).dict(),
                    },
                    message="User information updated successfully"
                )
                
            except Exception as e:
                logger.error(f"Error updating user: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to update user information: {e!s}"
                )
        
        @self.router.post("/change-password", response_model=StandardResponse)
        async def change_password(
            current_password: str,
            new_password: str,
            current_user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Change user password."""
            try:
                from .service import AuthService
                
                # Verify current password
                if not verify_password(current_password, current_user.hashed_password):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Current password is incorrect"
                    )
                
                # Update password
                auth_service = AuthService(conn)
                new_hashed_password = get_password_hash(new_password)
                
                await auth_service.update_user_password(
                    user_id=current_user.id,
                    hashed_password=new_hashed_password,
                )
                
                logger.info(f"Password changed for user {current_user.email}")
                
                return StandardResponse(
                    success=True,
                    message="Password changed successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error changing password: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to change password: {e!s}"
                )
    
    def _register_security_routes(self) -> None:
        """Register security operation routes."""
        
        @self.router.get("/security/audit", response_model=StandardResponse)
        async def get_security_audit(
            security_params: SecurityParams = Depends(),
            current_user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get security audit information for current user."""
            try:
                # Check admin permissions for detailed audit
                if security_params.audit_level == "detailed" and not current_user.is_admin:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Admin privileges required for detailed audit"
                    )
                
                from .service import AuthService
                
                auth_service = AuthService(conn)
                audit_info = await auth_service.get_security_audit(
                    user_id=current_user.id,
                    audit_level=security_params.audit_level,
                    include_logs=security_params.include_logs,
                    time_range_hours=security_params.time_range,
                )
                
                return StandardResponse(
                    success=True,
                    data=audit_info,
                    message="Security audit retrieved successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting security audit: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve security audit: {e!s}"
                )
        
        @self.router.get("/security/sessions", response_model=StandardResponse)
        async def get_user_sessions(
            current_user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get active sessions for current user."""
            try:
                from .service import AuthService
                
                auth_service = AuthService(conn)
                sessions = await auth_service.get_user_sessions(current_user.id)
                
                return StandardResponse(
                    success=True,
                    data=sessions,
                    message="User sessions retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting user sessions: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve user sessions: {e!s}"
                )
    
    def _register_session_routes(self) -> None:
        """Register session management routes."""
        
        @self.router.delete("/sessions/{session_id}", response_model=StandardResponse)
        async def terminate_session(
            session_id: str,
            current_user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Terminate a specific user session."""
            try:
                from .service import AuthService
                
                auth_service = AuthService(conn)
                success = await auth_service.terminate_session(
                    session_id=session_id,
                    user_id=current_user.id,
                )
                
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Session not found"
                    )
                
                return StandardResponse(
                    success=True,
                    message="Session terminated successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error terminating session: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to terminate session: {e!s}"
                )
        
        @self.router.delete("/sessions", response_model=StandardResponse)
        async def terminate_all_sessions(
            current_user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Terminate all sessions for current user."""
            try:
                from .service import AuthService
                
                auth_service = AuthService(conn)
                terminated_count = await auth_service.terminate_all_user_sessions(
                    user_id=current_user.id,
                )
                
                return StandardResponse(
                    success=True,
                    data={"terminated_sessions": terminated_count},
                    message=f"Terminated {terminated_count} sessions successfully"
                )
                
            except Exception as e:
                logger.error(f"Error terminating all sessions: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to terminate sessions: {e!s}"
                )
    
    def get_router(self) -> APIRouter:
        """Get the configured router instance."""
        return self.router


# Create and export router instance
_auth_router = UnifiedAuthRouter()
router = _auth_router.get_router()

# Export the router class and instance for registration
__all__ = ["UnifiedAuthRouter", "router"]