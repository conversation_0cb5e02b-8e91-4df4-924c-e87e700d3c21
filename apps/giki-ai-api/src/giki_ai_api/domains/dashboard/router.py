"""
Unified Dashboard Router
========================

Consolidated dashboard router using the UnifiedBaseRouter system.
Migrates from the original dashboard/router.py with standardized patterns.

This router consolidates:
- Dashboard metrics and KPIs
- Real-time data feeds
- Performance monitoring
- Financial summaries
- User activity tracking
- System health indicators
"""

import logging
from datetime import datetime, timedelta

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel

from ...core.dependencies import get_current_tenant_id, get_db_session
from ...shared.routers.base_router import (
    BaseRouterConfig,
    DateRangeParams,
    StandardResponse,
    UnifiedBaseRouter,
)
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .service import DashboardService

logger = logging.getLogger(__name__)


class DashboardFilterParams(BaseModel):
    """Filter parameters for dashboard data."""
    refresh: bool = Query(False, description="Force refresh cached data")
    include_trends: bool = Query(True, description="Include trend analysis")
    include_comparisons: bool = Query(True, description="Include period comparisons")
    granularity: str = Query("day", description="Data granularity (day, week, month)")


class MetricsParams(BaseModel):
    """Parameters for metrics requests."""
    metric_types: list[str] | None = Query(None, description="Specific metric types to include")
    include_forecasts: bool = Query(False, description="Include forecasted data")
    benchmark_period: str | None = Query(None, description="Benchmark comparison period")


class UnifiedDashboardRouter(UnifiedBaseRouter):
    """
    Unified dashboard router providing standardized dashboard functionality.
    
    Features:
    - Real-time dashboard metrics
    - Performance indicators
    - Trend analysis
    - Comparative reporting
    - Caching and optimization
    """
    
    def __init__(self):
        config = BaseRouterConfig(
            prefix="/api/v1/dashboard",
            tags=["Dashboard"],
            include_auth=True,
            include_tenant_isolation=True,
            include_caching=True,
            cache_ttl=60,  # 1 minute for dashboard data
            include_performance_monitoring=True,
        )
        super().__init__(config)
    
    def _register_routes(self) -> None:
        """Register all dashboard-related routes."""
        
        # Core dashboard data
        self._register_core_routes()
        
        # Metrics and KPIs
        self._register_metrics_routes()
        
        # Analytics and insights
        self._register_analytics_routes()
        
        # Real-time data
        self._register_realtime_routes()
    
    def _register_core_routes(self) -> None:
        """Register core dashboard routes."""
        
        @self.router.get("/", response_model=StandardResponse)
        async def get_dashboard_overview(
            filters: DashboardFilterParams = Depends(),
            date_range: DateRangeParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get complete dashboard overview with all key metrics."""
            try:
                service = DashboardService(conn, tenant_id)
                
                # Default to last 30 days if no date range specified
                if not date_range.start_date:
                    date_range.start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
                if not date_range.end_date:
                    date_range.end_date = datetime.now().strftime("%Y-%m-%d")
                
                dashboard_data = await service.get_dashboard_overview(
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                    include_trends=filters.include_trends,
                    include_comparisons=filters.include_comparisons,
                    force_refresh=filters.refresh,
                )
                
                return StandardResponse(
                    success=True,
                    data=dashboard_data,
                    message="Dashboard overview retrieved successfully",
                    metadata={
                        "date_range": {
                            "start": date_range.start_date,
                            "end": date_range.end_date,
                        },
                        "granularity": filters.granularity,
                        "cached": not filters.refresh,
                    }
                )
                
            except Exception as e:
                logger.error(f"Error getting dashboard overview: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve dashboard data: {e!s}"
                )
        
        @self.router.get("/summary", response_model=StandardResponse)
        async def get_dashboard_summary(
            date_range: DateRangeParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get a lightweight dashboard summary for quick loading."""
            try:
                service = DashboardService(conn, tenant_id)
                
                summary = await service.get_dashboard_metrics(
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                )
                
                return StandardResponse(
                    success=True,
                    data=summary,
                    message="Dashboard summary retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting dashboard summary: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve dashboard summary: {e!s}"
                )
        
        @self.router.get("/widgets/{widget_id}", response_model=StandardResponse)
        async def get_dashboard_widget(
            widget_id: str,
            date_range: DateRangeParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get data for a specific dashboard widget."""
            try:
                service = DashboardService(conn, tenant_id)
                
                widget_data = await service.get_widget_data(
                    widget_id=widget_id,
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                )
                
                if not widget_data:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Widget {widget_id} not found"
                    )
                
                return StandardResponse(
                    success=True,
                    data=widget_data,
                    message=f"Widget {widget_id} data retrieved successfully"
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Error getting widget {widget_id}: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve widget data: {e!s}"
                )
    
    def _register_metrics_routes(self) -> None:
        """Register metrics and KPI routes."""
        
        @self.router.get("/metrics", response_model=StandardResponse)
        async def get_dashboard_metrics(
            params: MetricsParams = Depends(),
            date_range: DateRangeParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get key performance indicators and metrics."""
            try:
                service = DashboardService(conn, tenant_id)
                
                metrics = await service.get_metrics(
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                    metric_types=params.metric_types,
                    include_forecasts=params.include_forecasts,
                    benchmark_period=params.benchmark_period,
                )
                
                return StandardResponse(
                    success=True,
                    data=metrics,
                    message="Metrics retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting metrics: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve metrics: {e!s}"
                )
        
        @self.router.get("/metrics/financial", response_model=StandardResponse)
        async def get_financial_metrics(
            date_range: DateRangeParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get financial KPIs and performance indicators."""
            try:
                service = DashboardService(conn, tenant_id)
                
                financial_metrics = await service.get_financial_metrics(
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                )
                
                return StandardResponse(
                    success=True,
                    data=financial_metrics,
                    message="Financial metrics retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting financial metrics: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve financial metrics: {e!s}"
                )
        
        @self.router.get("/metrics/operational", response_model=StandardResponse)
        async def get_operational_metrics(
            date_range: DateRangeParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get operational metrics and system performance."""
            try:
                service = DashboardService(conn, tenant_id)
                
                operational_metrics = await service.get_operational_metrics(
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                )
                
                return StandardResponse(
                    success=True,
                    data=operational_metrics,
                    message="Operational metrics retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting operational metrics: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve operational metrics: {e!s}"
                )
    
    def _register_analytics_routes(self) -> None:
        """Register analytics and insights routes."""
        
        @self.router.get("/analytics/trends", response_model=StandardResponse)
        async def get_trend_analysis(
            date_range: DateRangeParams = Depends(),
            metric: str = Query("transactions", description="Metric to analyze"),
            granularity: str = Query("day", description="Time granularity"),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get trend analysis for specific metrics."""
            try:
                service = DashboardService(conn, tenant_id)
                
                trends = await service.get_trend_analysis(
                    metric=metric,
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                    granularity=granularity,
                )
                
                return StandardResponse(
                    success=True,
                    data=trends,
                    message="Trend analysis retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting trend analysis: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve trend analysis: {e!s}"
                )
        
        @self.router.get("/analytics/insights", response_model=StandardResponse)
        async def get_dashboard_insights(
            date_range: DateRangeParams = Depends(),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get AI-powered insights and recommendations."""
            try:
                service = DashboardService(conn, tenant_id)
                
                insights = await service.get_ai_insights(
                    start_date=date_range.start_date,
                    end_date=date_range.end_date,
                )
                
                return StandardResponse(
                    success=True,
                    data=insights,
                    message="Dashboard insights retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting insights: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve insights: {e!s}"
                )
    
    def _register_realtime_routes(self) -> None:
        """Register real-time data routes."""
        
        @self.router.get("/realtime/status", response_model=StandardResponse)
        async def get_realtime_status(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get real-time system status and health indicators."""
            try:
                service = DashboardService(conn, tenant_id)
                status_data = await service.get_realtime_status()
                
                return StandardResponse(
                    success=True,
                    data=status_data,
                    message="Real-time status retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting real-time status: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve real-time status: {e!s}"
                )
        
        @self.router.get("/realtime/activity", response_model=StandardResponse)
        async def get_recent_activity(
            limit: int = Query(10, ge=1, le=50, description="Number of recent activities"),
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get recent user and system activity."""
            try:
                service = DashboardService(conn, tenant_id)
                activity = await service.get_recent_activity(limit=limit)
                
                return StandardResponse(
                    success=True,
                    data=activity,
                    message="Recent activity retrieved successfully"
                )
                
            except Exception as e:
                logger.error(f"Error getting recent activity: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve recent activity: {e!s}"
                )
    
    def get_router(self) -> APIRouter:
        """Get the configured router instance."""
        return self.router


# Create module-level router instance for backward compatibility
dashboard_router = UnifiedDashboardRouter()
router = dashboard_router.get_router()

# Export the router class for registration
__all__ = ["UnifiedDashboardRouter", "router"]