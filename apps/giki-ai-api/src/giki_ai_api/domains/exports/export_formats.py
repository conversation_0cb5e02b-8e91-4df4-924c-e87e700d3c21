"""
Export Format Definitions for Accounting Software
================================================

This module defines export formats for major accounting software including:
- QuickBooks (Desktop & Online)
- Zoho Books
- Tally Prime
- Xero
- FreshBooks
- Wave Accounting
- Sage

Each format includes:
- Column mappings
- Date/time formats
- Amount conventions
- File structure requirements
- Validation rules
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any

logger = logging.getLogger(__name__)


class ExportFormat(Enum):
    """Supported export formats for accounting software."""
    QUICKBOOKS_DESKTOP = "quickbooks_desktop"
    QUICKBOOKS_ONLINE = "quickbooks_online"
    QUICKBOOKS_CSV = "quickbooks_csv"  # Added for test compatibility
    ZOHO_BOOKS = "zoho_books"
    TALLY_PRIME = "tally_prime"
    XERO = "xero"
    FRESHBOOKS = "freshbooks"
    WAVE = "wave"
    SAGE = "sage"
    GENERIC_CSV = "generic_csv"
    GENERIC_EXCEL = "generic_excel"


class DateFormat(Enum):
    """Standard date formats used by accounting software."""
    MM_DD_YYYY = "%m/%d/%Y"
    DD_MM_YYYY = "%d/%m/%Y"
    YYYY_MM_DD = "%Y-%m-%d"
    MM_DD_YY = "%m/%d/%y"
    DD_MM_YY = "%d/%m/%y"
    MMMM_DD_YYYY = "%B %d, %Y"  # January 15, 2024
    YYYYMMDD = "%Y%m%d"  # ******** format used by Tally


class AmountFormat(Enum):
    """Amount format conventions."""
    POSITIVE_CREDIT = "positive_credit"  # Credits are positive, debits negative
    POSITIVE_DEBIT = "positive_debit"   # Debits are positive, credits negative
    ABSOLUTE_VALUES = "absolute_values"  # All positive with separate type column
    SIGNED_AMOUNT = "signed_amount"      # Single column with +/- values


@dataclass
class ColumnMapping:
    """Maps internal field names to export column names."""
    source_field: str
    target_column: str
    data_type: str = "text"
    format_function: str | None = None
    required: bool = True
    default_value: Any | None = None


class ExportFormatSpec:
    """Complete specification for an export format."""
    
    def __init__(self, **kwargs):
        """Initialize with flexible parameters for test compatibility."""
        # Basic information
        self.format_id = kwargs.get('format_id', ExportFormat.GENERIC_CSV)
        self.name = kwargs.get('name', 'Generic Export')
        self.description = kwargs.get('description', 'Generic export format')
        self.file_extension = kwargs.get('file_extension', '.csv')
        
        # Column specifications
        self.column_mappings = kwargs.get('column_mappings', kwargs.get('field_mappings', []))
        self.column_order = kwargs.get('column_order', [])
        
        # Format conventions
        self.date_format = kwargs.get('date_format', DateFormat.YYYY_MM_DD)
        self.amount_format = kwargs.get('amount_format', AmountFormat.SIGNED_AMOUNT)
        self.decimal_places = kwargs.get('decimal_places', 2)
        self.thousand_separator = kwargs.get('thousand_separator', ',')
        self.decimal_separator = kwargs.get('decimal_separator', '.')
        self.currency_symbol = kwargs.get('currency_symbol', '$')
        
        # CSV specific settings
        self.delimiter = kwargs.get('delimiter', ',')
        self.quote_char = kwargs.get('quote_char', '"')
        self.escape_char = kwargs.get('escape_char', '\\')
        self.line_terminator = kwargs.get('line_terminator', '\n')
        self.include_header = kwargs.get('include_header', kwargs.get('has_header', True))
        self.encoding = kwargs.get('encoding', 'utf-8')
        
        # Validation rules
        self.max_rows = kwargs.get('max_rows')
        self.max_file_size_mb = kwargs.get('max_file_size_mb')
        self.required_columns = kwargs.get('required_columns', kwargs.get('required_fields', []))
        
        # Special requirements
        self.requires_account_codes = kwargs.get('requires_account_codes', False)
        self.requires_tax_codes = kwargs.get('requires_tax_codes', False)
        self.requires_tracking_categories = kwargs.get('requires_tracking_categories', False)
        self.supports_multi_currency = kwargs.get('supports_multi_currency', False)
        
        # Additional metadata - store any extra parameters
        self.metadata = kwargs.get('metadata', {})
        
        # Store any unrecognized parameters in metadata for test compatibility
        recognized_params = {
            'format_id', 'name', 'description', 'file_extension', 'column_mappings', 
            'field_mappings', 'column_order', 'date_format', 'amount_format', 
            'decimal_places', 'thousand_separator', 'decimal_separator', 'currency_symbol',
            'delimiter', 'quote_char', 'escape_char', 'line_terminator', 'include_header',
            'has_header', 'encoding', 'max_rows', 'max_file_size_mb', 'required_columns',
            'required_fields', 'requires_account_codes', 'requires_tax_codes',
            'requires_tracking_categories', 'supports_multi_currency', 'metadata'
        }
        
        for key, value in kwargs.items():
            if key not in recognized_params:
                self.metadata[key] = value


class ExportFormatRegistry:
    """Registry of all supported export formats."""
    
    def __init__(self):
        self._formats: dict[ExportFormat, ExportFormatSpec] = {}
        self._initialize_formats()
    
    def _initialize_formats(self):
        """Initialize all supported export formats."""
        
        # QuickBooks Desktop
        self._register_quickbooks_desktop()
        
        # QuickBooks Online
        self._register_quickbooks_online()
        
        # Zoho Books
        self._register_zoho_books()
        
        # Tally Prime
        self._register_tally_prime()
        
        # Xero
        self._register_xero()
        
        # FreshBooks
        self._register_freshbooks()
        
        # Wave Accounting
        self._register_wave()
        
        # Sage
        self._register_sage()
        
        # Generic formats
        self._register_generic_csv()
        self._register_generic_excel()
    
    def _register_quickbooks_desktop(self):
        """Register QuickBooks Desktop IIF format."""
        self._formats[ExportFormat.QUICKBOOKS_DESKTOP] = ExportFormatSpec(
            format_id=ExportFormat.QUICKBOOKS_DESKTOP,
            name="QuickBooks Desktop (IIF)",
            description="Intuit Interchange Format for QuickBooks Desktop",
            file_extension="iif",
            column_mappings=[
                ColumnMapping("transaction_type", "!TRNS", default_value="GENERAL JOURNAL"),
                ColumnMapping("id", "TRNSID"),
                ColumnMapping("date", "DATE", format_function="format_date"),
                ColumnMapping("account_name", "ACCNT"),
                ColumnMapping("description", "NAME"),
                ColumnMapping("amount", "AMOUNT", format_function="format_amount"),
                ColumnMapping("memo", "MEMO"),
                ColumnMapping("category", "CLASS"),
                ColumnMapping("vendor", "NAME"),
            ],
            column_order=["!TRNS", "TRNSID", "DATE", "ACCNT", "NAME", "AMOUNT", "MEMO", "CLASS"],
            date_format=DateFormat.MM_DD_YYYY,
            amount_format=AmountFormat.SIGNED_AMOUNT,
            delimiter="\t",
            quote_char="",
            metadata={
                "header_required": "!TRNS\tTRNSID\tTRNSTYPE\tDATE\tACCNT\tNAME\tAMOUNT\tMEMO\tCLASS",
                "split_required": "!SPL\tSPLID\tTRNSTYPE\tDATE\tACCNT\tNAME\tAMOUNT\tMEMO\tCLASS",
                "end_marker": "!ENDTRNS"
            }
        )
    
    def _register_quickbooks_online(self):
        """Register QuickBooks Online CSV format."""
        self._formats[ExportFormat.QUICKBOOKS_ONLINE] = ExportFormatSpec(
            format_id=ExportFormat.QUICKBOOKS_ONLINE,
            name="QuickBooks Online",
            description="CSV format for QuickBooks Online import",
            file_extension="csv",
            column_mappings=[
                ColumnMapping("date", "Date", format_function="format_date"),
                ColumnMapping("description", "Description"),
                ColumnMapping("vendor", "Name"),
                ColumnMapping("amount", "Amount", format_function="format_amount"),
                ColumnMapping("category", "Category"),
                ColumnMapping("account_name", "Account"),
                ColumnMapping("memo", "Memo"),
                ColumnMapping("reference_number", "Ref No."),
                ColumnMapping("payment_method", "Payment Method"),
            ],
            column_order=["Date", "Description", "Name", "Amount", "Category", "Account", "Memo", "Ref No.", "Payment Method"],
            date_format=DateFormat.MM_DD_YYYY,
            amount_format=AmountFormat.SIGNED_AMOUNT,
            required_columns=["Date", "Description", "Amount"]
        )
    
    def _register_zoho_books(self):
        """Register Zoho Books CSV format."""
        self._formats[ExportFormat.ZOHO_BOOKS] = ExportFormatSpec(
            format_id=ExportFormat.ZOHO_BOOKS,
            name="Zoho Books",
            description="CSV format for Zoho Books import",
            file_extension="csv",
            column_mappings=[
                ColumnMapping("date", "Transaction Date", format_function="format_date"),
                ColumnMapping("reference_number", "Reference Number"),
                ColumnMapping("vendor", "Payee"),
                ColumnMapping("description", "Description"),
                ColumnMapping("category", "Category Name"),
                ColumnMapping("debit_amount", "Paid Out", format_function="format_amount"),
                ColumnMapping("credit_amount", "Paid In", format_function="format_amount"),
                ColumnMapping("payment_method", "Payment Mode"),
                ColumnMapping("notes", "Notes"),
            ],
            column_order=["Transaction Date", "Reference Number", "Payee", "Description", 
                         "Category Name", "Paid Out", "Paid In", "Payment Mode", "Notes"],
            date_format=DateFormat.DD_MM_YYYY,
            amount_format=AmountFormat.ABSOLUTE_VALUES,
            required_columns=["Transaction Date", "Description", "Paid Out", "Paid In"]
        )
    
    def _register_tally_prime(self):
        """Register Tally Prime XML format."""
        self._formats[ExportFormat.TALLY_PRIME] = ExportFormatSpec(
            format_id=ExportFormat.TALLY_PRIME,
            name="Tally Prime",
            description="XML format for Tally Prime import",
            file_extension="xml",
            column_mappings=[
                ColumnMapping("date", "DATE", format_function="format_tally_date"),
                ColumnMapping("voucher_type", "VOUCHERTYPENAME", default_value="Receipt"),
                ColumnMapping("voucher_number", "VOUCHERNUMBER"),
                ColumnMapping("party_name", "PARTYLEDGERNAME"),
                ColumnMapping("description", "NARRATION"),
                ColumnMapping("amount", "AMOUNT", format_function="format_amount"),
                ColumnMapping("gl_code", "LEDGERNAME"),
                ColumnMapping("cost_center", "COSTCENTRENAME"),
            ],
            column_order=["DATE", "VOUCHERTYPENAME", "VOUCHERNUMBER", "PARTYLEDGERNAME", 
                         "NARRATION", "AMOUNT", "LEDGERNAME", "COSTCENTRENAME"],
            date_format=DateFormat.YYYYMMDD,  # Tally uses YYYYMMDD format
            amount_format=AmountFormat.ABSOLUTE_VALUES,
            metadata={
                "xml_structure": "ENVELOPE/BODY/IMPORTDATA/REQUESTDATA/TALLYMESSAGE",
                "requires_header": True,
                "currency_symbol": "₹"  # Indian Rupees
            }
        )
    
    def _register_xero(self):
        """Register Xero CSV format."""
        self._formats[ExportFormat.XERO] = ExportFormatSpec(
            format_id=ExportFormat.XERO,
            name="Xero",
            description="CSV format for Xero import",
            file_extension="csv",
            column_mappings=[
                ColumnMapping("date", "*Date", format_function="format_date"),
                ColumnMapping("amount", "*Amount", format_function="format_amount"),
                ColumnMapping("vendor", "*Payee"),
                ColumnMapping("description", "*Description"),
                ColumnMapping("reference_number", "Reference"),
                ColumnMapping("gl_code", "*AccountCode"),
                ColumnMapping("tax_type", "*TaxType", default_value="Tax Exempt"),
            ],
            column_order=["*Date", "*Amount", "*Payee", "*Description", "Reference", 
                         "*AccountCode", "*TaxType"],
            date_format=DateFormat.DD_MM_YYYY,
            amount_format=AmountFormat.SIGNED_AMOUNT,
            required_columns=["*Date", "*Amount", "*Payee", "*Description", "*AccountCode", "*TaxType"],
            metadata={
                "asterisk_required": "Required fields must have asterisk (*) prefix"
            }
        )
    
    def _register_freshbooks(self):
        """Register FreshBooks CSV format."""
        self._formats[ExportFormat.FRESHBOOKS] = ExportFormatSpec(
            format_id=ExportFormat.FRESHBOOKS,
            name="FreshBooks",
            description="CSV format for FreshBooks import",
            file_extension="csv",
            column_mappings=[
                ColumnMapping("date", "Date", format_function="format_date"),
                ColumnMapping("vendor", "Vendor"),
                ColumnMapping("category", "Category"),
                ColumnMapping("amount", "Amount", format_function="format_amount"),
                ColumnMapping("currency", "Currency", default_value="USD"),
                ColumnMapping("description", "Notes"),
                ColumnMapping("tax_amount", "Tax Amount", default_value="0"),
            ],
            column_order=["Date", "Vendor", "Category", "Amount", "Currency", "Notes", "Tax Amount"],
            date_format=DateFormat.YYYY_MM_DD,
            amount_format=AmountFormat.POSITIVE_DEBIT,
            required_columns=["Date", "Vendor", "Category", "Amount"],
            supports_multi_currency=True
        )
    
    def _register_wave(self):
        """Register Wave Accounting CSV format."""
        self._formats[ExportFormat.WAVE] = ExportFormatSpec(
            format_id=ExportFormat.WAVE,
            name="Wave Accounting",
            description="CSV format for Wave Accounting import",
            file_extension="csv",
            column_mappings=[
                ColumnMapping("date", "Transaction Date", format_function="format_date"),
                ColumnMapping("description", "Description"),
                ColumnMapping("amount", "Amount", format_function="format_amount"),
                ColumnMapping("category", "Account"),
                ColumnMapping("vendor", "Customer/Vendor"),
                ColumnMapping("invoice_number", "Invoice Number"),
            ],
            column_order=["Transaction Date", "Description", "Amount", "Account", 
                         "Customer/Vendor", "Invoice Number"],
            date_format=DateFormat.YYYY_MM_DD,
            amount_format=AmountFormat.SIGNED_AMOUNT,
            required_columns=["Transaction Date", "Description", "Amount", "Account"]
        )
    
    def _register_sage(self):
        """Register Sage CSV format."""
        self._formats[ExportFormat.SAGE] = ExportFormatSpec(
            format_id=ExportFormat.SAGE,
            name="Sage",
            description="CSV format for Sage import",
            file_extension="csv",
            column_mappings=[
                ColumnMapping("transaction_type", "Type", default_value="SI"),
                ColumnMapping("account_reference", "Account Reference"),
                ColumnMapping("nominal_code", "Nominal A/C Ref"),
                ColumnMapping("department", "Dept"),
                ColumnMapping("date", "Date", format_function="format_date"),
                ColumnMapping("reference_number", "Reference"),
                ColumnMapping("description", "Details"),
                ColumnMapping("net_amount", "Net Amount", format_function="format_amount"),
                ColumnMapping("tax_code", "Tax Code", default_value="T0"),
                ColumnMapping("tax_amount", "Tax Amount", default_value="0.00"),
            ],
            column_order=["Type", "Account Reference", "Nominal A/C Ref", "Dept", "Date",
                         "Reference", "Details", "Net Amount", "Tax Code", "Tax Amount"],
            date_format=DateFormat.DD_MM_YYYY,
            amount_format=AmountFormat.ABSOLUTE_VALUES,
            required_columns=["Type", "Account Reference", "Nominal A/C Ref", "Date", 
                            "Reference", "Details", "Net Amount", "Tax Code"],
            requires_account_codes=True,
            requires_tax_codes=True
        )
    
    def _register_generic_csv(self):
        """Register generic CSV format."""
        self._formats[ExportFormat.GENERIC_CSV] = ExportFormatSpec(
            format_id=ExportFormat.GENERIC_CSV,
            name="Generic CSV",
            description="Standard CSV format with all transaction fields",
            file_extension="csv",
            column_mappings=[
                ColumnMapping("date", "Date", format_function="format_date"),
                ColumnMapping("description", "Description"),
                ColumnMapping("vendor", "Vendor/Payee"),
                ColumnMapping("amount", "Amount", format_function="format_amount"),
                ColumnMapping("debit_amount", "Debit", format_function="format_amount"),
                ColumnMapping("credit_amount", "Credit", format_function="format_amount"),
                ColumnMapping("category", "Category"),
                ColumnMapping("gl_code", "GL Code"),
                ColumnMapping("account_name", "Account"),
                ColumnMapping("reference_number", "Reference"),
                ColumnMapping("memo", "Memo/Notes"),
                ColumnMapping("payment_method", "Payment Method"),
                ColumnMapping("check_number", "Check Number"),
            ],
            column_order=["Date", "Description", "Vendor/Payee", "Amount", "Debit", "Credit",
                         "Category", "GL Code", "Account", "Reference", "Memo/Notes", 
                         "Payment Method", "Check Number"],
            date_format=DateFormat.MM_DD_YYYY,
            amount_format=AmountFormat.ABSOLUTE_VALUES,
            required_columns=["Date", "Description", "Amount"]
        )
    
    def _register_generic_excel(self):
        """Register generic Excel format."""
        self._formats[ExportFormat.GENERIC_EXCEL] = ExportFormatSpec(
            format_id=ExportFormat.GENERIC_EXCEL,
            name="Generic Excel",
            description="Standard Excel format with all transaction fields and formatting",
            file_extension="xlsx",
            column_mappings=[
                ColumnMapping("date", "Date", format_function="format_date"),
                ColumnMapping("description", "Description"),
                ColumnMapping("vendor", "Vendor/Payee"),
                ColumnMapping("amount", "Amount", format_function="format_amount"),
                ColumnMapping("debit_amount", "Debit", format_function="format_amount"),
                ColumnMapping("credit_amount", "Credit", format_function="format_amount"),
                ColumnMapping("balance", "Balance", format_function="format_amount"),
                ColumnMapping("category", "Category"),
                ColumnMapping("gl_code", "GL Code"),
                ColumnMapping("account_name", "Account"),
                ColumnMapping("reference_number", "Reference"),
                ColumnMapping("memo", "Memo/Notes"),
                ColumnMapping("payment_method", "Payment Method"),
                ColumnMapping("check_number", "Check Number"),
                ColumnMapping("ai_confidence", "AI Confidence %", data_type="percentage"),
            ],
            column_order=["Date", "Description", "Vendor/Payee", "Amount", "Debit", "Credit",
                         "Balance", "Category", "GL Code", "Account", "Reference", "Memo/Notes", 
                         "Payment Method", "Check Number", "AI Confidence %"],
            date_format=DateFormat.MM_DD_YYYY,
            amount_format=AmountFormat.ABSOLUTE_VALUES,
            metadata={
                "formatting": {
                    "header_style": {"bold": True, "bg_color": "#295343", "font_color": "white"},
                    "date_format": "mm/dd/yyyy",
                    "amount_format": "$#,##0.00",
                    "percentage_format": "0%",
                    "freeze_panes": "A2",
                    "auto_filter": True,
                    "column_widths": {
                        "Date": 12,
                        "Description": 40,
                        "Vendor/Payee": 25,
                        "Amount": 15,
                        "Category": 20,
                        "GL Code": 10
                    }
                }
            }
        )
    
    def get_format(self, format_id: ExportFormat) -> ExportFormatSpec | None:
        """Get export format specification by ID."""
        return self._formats.get(format_id)
    
    def get_all_formats(self) -> dict[ExportFormat, ExportFormatSpec]:
        """Get all registered export formats."""
        return self._formats.copy()
    
    def get_formats_by_extension(self, extension: str) -> list[ExportFormatSpec]:
        """Get all formats that use a specific file extension."""
        extension = extension.lower().lstrip(".")
        return [
            spec for spec in self._formats.values()
            if spec.file_extension.lower() == extension
        ]
    
    def validate_data_for_format(
        self, 
        data: list[dict[str, Any]], 
        format_id: ExportFormat
    ) -> tuple[bool, list[str]]:
        """
        Validate transaction data against format requirements.
        
        Returns:
            Tuple of (is_valid, error_messages)
        """
        format_spec = self.get_format(format_id)
        if not format_spec:
            return False, [f"Unknown format: {format_id}"]
        
        errors = []
        
        # Check required columns
        for mapping in format_spec.column_mappings:
            if mapping.required and mapping.default_value is None:
                # Check if any row is missing this field
                for i, row in enumerate(data):
                    if mapping.source_field not in row or row[mapping.source_field] is None:
                        errors.append(
                            f"Row {i+1}: Missing required field '{mapping.source_field}'"
                        )
        
        # Check row count limit
        if format_spec.max_rows and len(data) > format_spec.max_rows:
            errors.append(
                f"Too many rows: {len(data)} exceeds limit of {format_spec.max_rows}"
            )
        
        # Format-specific validations
        if format_spec.requires_account_codes:
            for i, row in enumerate(data):
                if not row.get("gl_code") and not row.get("account_code"):
                    errors.append(f"Row {i+1}: Missing required account code")
        
        if format_spec.requires_tax_codes:
            for i, row in enumerate(data):
                if not row.get("tax_code"):
                    errors.append(f"Row {i+1}: Missing required tax code")
        
        return len(errors) == 0, errors


# Global registry instance
export_format_registry = ExportFormatRegistry()


# Format conversion functions
def format_date(value: Any, date_format: DateFormat) -> str:
    """Format date according to specified format."""
    if isinstance(value, str):
        # Try to parse common date formats
        for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y"]:
            try:
                value = datetime.strptime(value, fmt)
                break
            except ValueError:
                continue
    
    if isinstance(value, datetime):
        return value.strftime(date_format.value)
    
    return str(value)


def format_amount(value: Any, decimal_places: int = 2) -> str:
    """Format amount with specified decimal places."""
    try:
        amount = float(value)
        return f"{amount:.{decimal_places}f}"
    except (ValueError, TypeError):
        return "0.00"


def format_tally_date(value: Any) -> str:
    """Format date for Tally Prime (YYYYMMDD)."""
    if isinstance(value, str):
        # Try to parse common date formats
        for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y"]:
            try:
                value = datetime.strptime(value, fmt)
                break
            except ValueError:
                continue
    
    if isinstance(value, datetime):
        return value.strftime("%Y%m%d")
    
    return str(value)


class ExportFormatsService:
    """Service for managing export format configurations."""
    
    def __init__(self, conn, tenant_id: int):
        """Initialize with database connection and tenant ID."""
        self.conn = conn
        self.tenant_id = tenant_id
        self.registry = export_format_registry
    
    async def get_available_formats(self) -> list[dict[str, Any]]:
        """Get all available export formats for the tenant."""
        formats = []
        
        for format_enum, format_spec in self.registry.get_all_formats().items():
            format_info = {
                "id": format_enum.value,
                "name": format_spec.name,
                "description": format_spec.description,
                "file_extension": format_spec.file_extension,
                "supports_multi_currency": format_spec.supports_multi_currency,
                "requires_account_codes": format_spec.requires_account_codes,
                "required_fields": [mapping.source_field for mapping in format_spec.column_mappings if mapping.required]
            }
            formats.append(format_info)
        
        return formats
    
    async def get_format_spec(self, format_id: str) -> ExportFormatSpec | None:
        """Get format specification by ID."""
        try:
            format_enum = ExportFormat(format_id)
            return self.registry.get_format(format_enum)
        except ValueError:
            return None
    
    async def validate_format_requirements(self, format_id: str, data: list[dict[str, Any]]) -> tuple[bool, list[str]]:
        """Validate data against format requirements."""
        try:
            format_enum = ExportFormat(format_id)
            return self.registry.validate_data_for_format(data, format_enum)
        except ValueError:
            return False, [f"Unknown format: {format_id}"]