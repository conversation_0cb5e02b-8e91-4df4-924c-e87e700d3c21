"""
Export Router
=============

Unified export router providing accounting format export endpoints.
Connects frontend ExportValidationSystem to backend export functionality.

Expected endpoints:
- GET /api/v1/exports/formats - Get available export formats
- POST /api/v1/exports/readiness/{format_id} - Validate export readiness
- POST /api/v1/exports/download - Download export file
"""

import logging
from typing import Any

from asyncpg import Connection
from fastapi import APIRouter, Depends, HTTPException, Path, status
from pydantic import BaseModel

from ...core.dependencies import get_current_tenant_id, get_db_session
from ...shared.routers.base_router import (
    BaseRouterConfig,
    DateRangeParams,
    StandardResponse,
    UnifiedBaseRouter,
)
from ..auth.models import User
from ..auth.secure_auth import get_current_active_user
from .export_agent import ExportAgent
from .export_formats import ExportFormatsService

logger = logging.getLogger(__name__)


class ExportReadinessRequest(BaseModel):
    """Request for export readiness validation."""
    date_from: str | None = None
    date_to: str | None = None
    category_ids: list[int] | None = None
    account_ids: list[int] | None = None


class ExportDownloadRequest(BaseModel):
    """Request for export download."""
    format_id: str
    date_from: str | None = None
    date_to: str | None = None
    category_ids: list[int] | None = None
    filename: str | None = None


class ExportRouter(UnifiedBaseRouter):
    """Unified export router for accounting format exports."""
    
    def __init__(self):
        config = BaseRouterConfig(
            prefix="/api/v1/exports",
            tags=["Exports"]
        )
        super().__init__(config)
        self._register_export_routes()
    
    def _register_export_routes(self) -> None:
        """Register export functionality routes."""
        
        @self.router.get("/formats", response_model=StandardResponse)
        async def get_export_formats(
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Get available export formats."""
            try:
                service = ExportFormatsService(conn, tenant_id)
                formats = await service.get_available_formats()
                
                return StandardResponse(
                    success=True,
                    data=formats,
                    message="Export formats retrieved successfully"
                )
            except Exception as e:
                logger.error(f"Failed to get export formats: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve export formats: {e!s}"
                )
        
        @self.router.post("/readiness/{format_id}", response_model=StandardResponse)
        async def validate_export_readiness(
            format_id: str = Path(..., description="Export format ID"),
            request: ExportReadinessRequest = ...,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Validate export readiness for specified format."""
            try:
                agent = ExportAgent(db=conn)
                
                # Build filters from request
                filters = {}
                if request.date_from:
                    filters["date_from"] = request.date_from
                if request.date_to:
                    filters["date_to"] = request.date_to
                if request.category_ids:
                    filters["category_ids"] = request.category_ids
                if request.account_ids:
                    filters["account_ids"] = request.account_ids
                
                readiness = await agent.validate_export_readiness(
                    tenant_id=tenant_id,
                    export_format=format_id,
                    filters=filters
                )
                
                return StandardResponse(
                    success=True,
                    data=readiness,
                    message="Export readiness validated successfully"
                )
            except Exception as e:
                logger.error(f"Failed to validate export readiness: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to validate export readiness: {e!s}"
                )
        
        @self.router.post("/download", response_model=StandardResponse)
        async def download_export(
            request: ExportDownloadRequest = ...,
            tenant_id: int = Depends(get_current_tenant_id),
            user: User = Depends(get_current_active_user),
            conn: Connection = Depends(get_db_session),
        ):
            """Download export file in specified format."""
            try:
                agent = ExportAgent(db=conn)
                export_result = await agent.generate_export(
                    format_id=request.format_id,
                    date_from=request.date_from,
                    date_to=request.date_to,
                    category_ids=request.category_ids,
                    filename=request.filename
                )
                
                return StandardResponse(
                    success=True,
                    data=export_result,
                    message="Export generated successfully"
                )
            except Exception as e:
                logger.error(f"Failed to generate export: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to generate export: {e!s}"
                )


# Create router instance
def get_router() -> APIRouter:
    """Get the export router instance."""
    return ExportRouter().router