# Development Patterns - Technical Excellence Framework

## WORKSPACE ORGANIZATION PATTERNS

### Workspace Root Rule (ABSOLUTE)
**NO CODE, TESTS, SCRIPTS, OR DOCS IN WORKSPACE ROOT**
- Only allowed files in root: CLAUDE.md, README.md, configuration files
- All code → apps/
- All tests → apps/*/tests/
- All scripts → scripts/ or apps/*/scripts/
- All docs → docs/
- **Zero tolerance policy - no exceptions**

### File Structure Organization
- **MIS Platform Architecture**: apps/giki-ai-api (FastAPI) + apps/giki-ai-app (React)
- **Test Data Centralization**: ALL test data in `libs/test-data/` - NO scattered files
- **Script Organization**: Development scripts in scripts/, app-specific in apps/*/scripts/
- **Documentation Structure**: Organized in docs/ with numbered priority system

## COMMAND EXECUTION PATTERNS

### Bun-First Principle (CLEAN BUN+UV MONOREPO)
**NEVER USE PNPM COMMANDS** - All development operations use direct bun scripts from workspace root

#### Command Execution Context
```bash
# ALWAYS execute from workspace root - verify before EVERY command sequence:
pwd  # Must show: /Users/<USER>/giki-ai-workspace/

# For ALL commands, use direct bun scripts from workspace root:
bun lint:api         # Runs ruff in api directory, returns to root
bun serve:api        # Starts uvicorn server, returns to root
bun build:app        # Runs vite build, returns to root
```

#### BUN Scripts Quick Reference
```bash
# DEVELOPMENT - Direct execution (terminal output)
bun serve:all              # Both API:8000 + frontend:4200 with concurrently
bun serve:api              # uvicorn FastAPI server
bun serve:app              # Vite development server

# QUALITY - Background execution (prevents timeouts)
bun lint:api:bg            # Python ruff linting in background
bun lint:app               # Frontend ESLint linting (background via script)

# TESTING - Background execution (prevents timeouts, auto-starts services)
bun test:api:bg [unit|integration|slow|all]  # API pytest in background
bun test:app:bg [unit|coverage|visual|integration|e2e]  # Frontend testing in background
bun test:visual                              # Visual regression tests (auto-starts servers)
bun test:e2e                                 # E2E tests (auto-starts all services)
bun test:e2e                                # E2E tests (auto-starts servers)

# BUILD - Background execution (prevents timeouts)
bun build:api              # FastAPI ready message (no build needed)
bun build:app:bg           # Vite production build in background

# LOG READING - Direct file access (NO BLOCKING)
tail -n 50 logs/serve-*.log   # Server status (API + Frontend)
tail -n 50 logs/test-*.log    # Test results (API + Frontend + E2E)
tail -n 50 logs/lint-*.log    # Linting results (API + Frontend)
tail -n 50 logs/build-*.log   # Build results (API + Frontend)
tail -n 50 logs/api-errors.log # API-specific errors

# UTILITIES
bun db                     # Test PostgreSQL connection (giki_ai_dev database)
```

#### Critical Execution Rules
1. **ALWAYS use bun scripts** - Never bypass with direct commands
2. **BACKGROUND EXECUTION** - Use background scripts (lint:api:bg, test:app:bg, etc.) to avoid timeout issues
3. **LOG OVERWRITE** - All background scripts overwrite logs (>) not append (>>) to prevent massive files
4. **AUTO-START SERVICES** - Scripts automatically start PostgreSQL, Redis, API/Frontend servers when needed
5. **Workspace root execution** - All scripts run from workspace root
6. **IMPROVE bun commands** - When scripts fail, fix the scripts rather than bypassing them
7. **SUBSHELL PATTERN** - All bun scripts use `(cd dir && command)` to maintain workspace root

## CLAUDE CODE OPTIMIZATION PATTERNS

### Background-First Execution Strategy
**ALL SLOW OPERATIONS RUN IN BACKGROUND WITH LOG OVERWRITE**

```typescript
interface ClaudeCodeOptimizedWorkflow {
  // Background scripts prevent timeouts
  backgroundCommands: {
    "bun lint:app": "Background frontend linting (never direct)",
    "bun test:api:bg unit": "Background API unit tests",
    "bun test:app:bg unit": "Background frontend unit tests",
    "bun test:visual": "Auto-starts all services, background execution",
    "bun test:e2e": "Auto-starts all services, background execution"
  };
  
  // Fast commands work directly
  fastCommands: {
    "bun lint:api": "Python linting is fast enough",
    "bun db": "Database test after auto-start",
    "bun logs": "Status monitoring"
  };
  
  // Auto-service startup
  smartServices: {
    "PostgreSQL": "Auto-started by serve:api and test scripts",
    "Redis": "Auto-started with graceful fallback if unavailable",
    "API/Frontend": "Auto-started by test scripts when needed"
  };
}
```

### Service Management Patterns
- **AUTO-START SERVICES**: All test commands automatically start PostgreSQL, Redis, servers as needed
- **PORT DETECTION**: Scripts intelligently detect running services to avoid conflicts
- **LOG OVERWRITE**: Background scripts overwrite logs (>) to keep files manageable for Claude
- **NO TIMEOUTS**: Background execution eliminates Claude Code timeout issues

### Eliminated Inefficient Practices
**Timeout-Prone Commands (NEVER USE):**
- ❌ `bun lint:app` direct → ✅ `bun lint:app` (background via script)
- ❌ `bun test:api` → ✅ `bun test:api:bg [unit|integration|slow|all]`
- ❌ `bun test:app` → ✅ `bun test:app:bg [unit|coverage|visual|integration|e2e]`
- ❌ `bun build:app` → ✅ `bun build:app:bg`
- ❌ `bun logs:*` commands → ✅ `tail -n 50 logs/*.log` (direct file reading)

**Blocking Operations (ELIMINATED):**
- ❌ `bun logs:test` (blocks indefinitely) → ✅ `tail -n 50 logs/test-*.log`
- ❌ `bun logs:serve` (blocks indefinitely) → ✅ `tail -n 50 logs/serve-*.log`
- ❌ Direct curl commands (require manual approval) → ✅ Use scripts/api-tester.py for automated testing
- ❌ Creating new shell scripts (require manual approval) → ✅ Add parameters to existing scripts
- ❌ Manual curl approval workflows → ✅ Enhanced existing script functionality
- ❌ Existing test infrastructure for API validation → ✅ Enhanced bun test commands

**Manual Service Management (ELIMINATED):**
- ❌ Manual PostgreSQL/Redis startup → ✅ Auto-started by scripts
- ❌ Manual API/Frontend startup → ✅ Auto-started when needed
- ❌ Log rotation → ✅ Log overwrite strategy
- ❌ Manual process cleanup → ✅ Automatic management

## MIS-FIRST ARCHITECTURE PATTERNS

### Core Platform Principles
- **UNIFIED PRODUCT**: Every customer gets a complete MIS with progressive enhancement opportunities
- **CATEGORIZATION**: Hierarchical categorization with AI-powered accuracy improvements
- **ACCURACY FOCUS**: Business value through accuracy improvements, not technical complexity

### Development Efficiency Patterns
- **API TESTER**: Use scripts/api-tester.py for automated authentication and endpoint testing
- **ENHANCE EXISTING SCRIPTS**: NEVER create new shell scripts - enhance existing ones to avoid manual approval
- **NO MANUAL APPROVAL COMMANDS**: NEVER use curl commands directly OR create new scripts - use existing scripts
- **PROGRESSIVE ENHANCEMENT**: Add functionality to existing scripts like api-tester.py instead of new files
- **DIRECT LOG READING**: Use `tail -n 50 logs/*.log` instead of blocking bun logs commands
- **PARALLEL EXECUTION**: Run independent tools simultaneously for 40-60% time savings
- **ZERO TOLERANCE FOR FAKE DATA**: NEVER create mock data, placeholder values, simulated responses

### Platform Detection & Branch Strategy
**CRITICAL**: Always detect platform first to determine correct development branch
- **master** - macOS/Darwin development (primary)
- **poppy** - Pop!_OS/Linux development 
- **windy** - Windows development (future)

```bash
uname -a                    # Darwin = master, Linux = poppy, Windows = windy
git branch --show-current   # Verify current branch matches platform
```

## ANTI-FRAGMENTATION PATTERNS

### Core Anti-Fragmentation Principle
**NEVER CREATE MULTIPLE VERSIONS - ALWAYS MODIFY EXISTING FILES**
Enhance existing files rather than creating simplified/alternate/fixed versions. This prevents workspace fragmentation and maintains single sources of truth across ALL file types: code, tests, scripts, configs, documentation.

### File Modification Patterns
- **Single Source of Truth**: One file per functionality, no duplicates
- **Enhancement Over Creation**: Improve existing files rather than creating new ones
- **Version Consolidation**: Merge multiple versions into single enhanced version
- **Documentation Evolution**: Update existing docs rather than creating new ones

## ENVIRONMENT CONFIGURATION PATTERNS

### Environment Management Strategy
- **Environment Files**: Keep development and production env files in `infrastructure/environments/`
- **Service Account Paths**: Store GCP service account file paths in env files (later replaced with secrets)
- **Auto-Loading**: Config system automatically loads from infrastructure directory based on ENVIRONMENT variable
- **Security**: Production secrets will be managed through Google Cloud Secret Manager

### Configuration Hierarchy
1. **Development**: Local development with test data and development services
2. **Staging**: Production-like environment for final validation
3. **Production**: Live environment with real data and production services

## SCREENSHOT ANALYSIS PATTERNS

### Unified Analysis Tool Usage
- **UNIFIED TOOL**: Use `mcp__screenshot__analyze_screenshots` for all visual analysis
- **PROBLEM-DISCOVERY FOCUS**: Tool configured to FIND PROBLEMS, not validate success
- **DOCUMENTATION CONTEXT**: Always include relevant docs (05-DESIGN-SYSTEM.md, etc.)
- **MANDATORY WORKFLOW**: Capture → Read images → Analyze with context

### Tool Parameters & Usage
```typescript
interface AnalyzeScreenshotsArgs {
  image_paths: string[];           // Array of screenshot file paths (required)
  markdown_paths?: string[];       // Optional documentation context files  
  query: string;                   // Specific analysis question (required)
}

// Example Usage Patterns
const SCREENSHOT_ANALYSIS_PATTERNS = {
  // Brand Compliance Validation  
  brandCompliance: {
    image_paths: ["/path/to/screenshots/development/page-current.png"],
    markdown_paths: ["/path/to/docs/05-DESIGN-SYSTEM.md"],
    query: "Audit for brand compliance violations: check for prohibited emoji icons (🎯🚀💡📊✨🔄⚡✓), non-#295343 colors, spacing violations, typography inconsistencies."
  }
};
```

### Visual Analysis Workflow
```typescript
interface ScreenshotAnalysisWorkflow {
  capture: "Take screenshots of current state";
  read: "Read screenshot files to see actual content";
  analyze: "Use MCP screenshot analysis with documentation context";
  document: "Record findings and required changes";
  implement: "Make necessary corrections based on analysis";
}
```

## TOOL SELECTION PATTERNS

### Intelligent Tool Selection Strategy
- **Parallel Execution**: Run independent tools simultaneously (40-60% time savings)
- **Task Tool**: Use for complex searches; avoid for known paths or simple operations
- **Thinking Depth**: ULTRATHINK (architecture), think harder (analysis), think (simple checks)

```typescript
interface ToolSelectionStrategy {
  // Use Task tool for complex, open-ended searches
  complexSearchScenarios: {
    keywordSearch: "When searching for 'config' or 'logger' across codebase";
    fileDiscovery: "When asking 'which file does X?' without specific location";
    patternIdentification: "When looking for usage patterns across multiple files";
    architectureAnalysis: "When understanding system-wide relationships";
  };
  
  // Use direct tools for specific, known targets
  directToolScenarios: {
    specificFiles: "When reading known file paths, use Read tool directly";
    classDefinitions: "When searching for 'class Foo', use Glob tool directly";
    limitedScope: "When searching within 2-3 specific files, use Read tool";
    knownPatterns: "When looking for specific code patterns in known locations";
  };
}
```

---

**TECHNICAL EXCELLENCE**: These patterns ensure consistent, high-quality development practices across all project work.